<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MT5交易系统 - 系统设置</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .content {
            padding: 20px;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            font-weight: bold;
        }
        .form-switch .form-check-input {
            width: 3em;
            height: 1.5em;
        }
        .form-switch .form-check-input:checked {
            background-color: #28a745;
            border-color: #28a745;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">MT5交易系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">控制面板</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('signals') }}">信号管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('orders') }}">订单管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">交易报告</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('settings') }}">系统设置</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <main class="col-12 px-4 py-3">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统设置</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="post">
                    <!-- MT5 连接配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-gear"></i> MetaTrader 5 连接配置
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>安全提示：</strong> 修改MT5连接配置后，系统将自动重启连接。请确保输入的信息准确无误。
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>使用说明：</strong>
                                <ol class="mb-0 mt-2">
                                    <li>首先点击"检查环境"确认MT5是否正确安装</li>
                                    <li>填写完整的MT5账户信息（账号、密码、服务器）</li>
                                    <li>点击"测试连接"验证配置是否正确</li>
                                    <li>如果测试成功，点击"保存设置"应用配置</li>
                                </ol>
                                <div class="mt-2">
                                    <strong>常见问题：</strong>
                                    <ul class="mb-0">
                                        <li><strong>授权失败：</strong> 检查账号、密码、服务器名称是否正确</li>
                                        <li><strong>初始化失败：</strong> 确保MT5客户端已安装且路径正确</li>
                                        <li><strong>连接超时：</strong> 检查网络连接和防火墙设置</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="mt5_login" class="form-label">MT5账号ID</label>
                                    <input type="text" class="form-control" id="mt5_login" name="mt5_login"
                                           value="{{ config.mt5_login }}" placeholder="请输入MT5账号ID（纯数字）">
                                    <div class="form-text">
                                        MT5交易账号ID，必须是纯数字格式
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="mt5_password" class="form-label">MT5密码</label>
                                    <input type="password" class="form-control" id="mt5_password" name="mt5_password"
                                           value="{{ config.mt5_password }}" placeholder="请输入MT5账号密码">
                                    <div class="form-text">
                                        MT5交易账号密码
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="mt5_server" class="form-label">MT5服务器</label>
                                    <input type="text" class="form-control" id="mt5_server" name="mt5_server"
                                           value="{{ config.mt5_server }}" placeholder="例如: Tickmill-Demo" list="server_suggestions">
                                    <datalist id="server_suggestions">
                                        <option value="Tickmill-Demo">
                                        <option value="Tickmill-Live">
                                        <option value="demo.mt5tickmill.com">
                                        <option value="live.tickmill.com">
                                        <option value="XM-Demo">
                                        <option value="XM-Real">
                                        <option value="FXTM-Demo">
                                        <option value="FXTM-Real">
                                        <option value="Exness-Demo">
                                        <option value="Exness-Real">
                                        <option value="IC Markets-Demo">
                                        <option value="IC Markets-Live">
                                    </datalist>
                                    <div class="form-text">
                                        MT5服务器名称，必须与经纪商提供的名称完全一致。可以从下拉列表中选择常见服务器或手动输入。
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="mt5_path" class="form-label">MT5安装路径</label>
                                    <input type="text" class="form-control" id="mt5_path" name="mt5_path"
                                           value="{{ config.mt5_path }}" placeholder="C:\Program Files\MetaTrader 5\terminal64.exe">
                                    <div class="form-text">
                                        MT5客户端的完整安装路径
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button type="button" class="btn btn-outline-info" id="checkMT5EnvironmentBtn">
                                            <i class="bi bi-search"></i> 检查环境
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="testMT5ConnectionBtn">
                                            <i class="bi bi-wifi"></i> 测试连接
                                        </button>
                                    </div>
                                    <div class="form-text">
                                        先点击"检查环境"确认MT5安装状态，再点击"测试连接"验证账户信息
                                    </div>
                                    <div id="mt5EnvironmentResult" class="mt-2" style="display: none;"></div>
                                    <div id="mt5TestResult" class="mt-2" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- MT5 服务器信息 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">MetaTrader 5 服务器信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <strong>MT5服务器时间:</strong> <span id="mt5-server-time">{{ server_time }}</span>
                                        <div class="form-text">这是交易服务器的当前时间，可用于配置交易时段</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <strong>北京时间:</strong> <span id="beijing-time">{{ beijing_time }}</span>
                                        <div class="form-text">当前北京时间（GMT+8）</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 交易开关 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">交易设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check form-switch mb-4">
                                <input class="form-check-input" type="checkbox" id="enable_trading" name="enable_trading" {% if config.enable_trading %}checked{% endif %}>
                                <label class="form-check-label" for="enable_trading">
                                    启用交易功能
                                </label>
                                <div class="form-text">
                                    开启后系统将自动根据接收到的信号执行交易
                                </div>
                            </div>
                            
                            <div class="form-check form-switch mb-4">
                                <input class="form-check-input" type="checkbox" id="enable_time_filter" name="enable_time_filter" {% if config.enable_time_filter %}checked{% endif %}>
                                <label class="form-check-label" for="enable_time_filter">
                                    启用交易时段过滤
                                </label>
                                <div class="form-text">
                                    开启后系统只在设定的时间段内执行交易
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="default_volume" class="form-label">默认交易量</label>
                                    <input type="number" class="form-control" id="default_volume" name="default_volume" step="0.01" min="0.01" value="{{ config.default_volume }}">
                                    <div class="form-text">
                                        默认交易手数，如未针对特定交易对配置则使用此值
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="default_sl_points" class="form-label">默认止损点数</label>
                                    <input type="number" class="form-control" id="default_sl_points" name="default_sl_points" step="1" min="1" value="{{ config.default_sl_points }}">
                                    <div class="form-text">
                                        默认止损点数，计算止损价格时使用
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="default_tp_points" class="form-label">默认止盈点数</label>
                                    <input type="number" class="form-control" id="default_tp_points" name="default_tp_points" step="1" min="1" value="{{ config.default_tp_points }}">
                                    <div class="form-text">
                                        默认止盈点数，计算止盈价格时使用
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="sl_tp_calculation_method" class="form-label">止损止盈计算方式</label>
                                    <select class="form-select" id="sl_tp_calculation_method" name="sl_tp_calculation_method">
                                        <option value="points" {% if config.sl_tp_calculation_method == 'points' %}selected{% endif %}>使用点数计算</option>
                                        <option value="signal_levels" {% if config.sl_tp_calculation_method == 'signal_levels' %}selected{% endif %}>使用信号级别(R2/S2/MEAN)</option>
                                    </select>
                                    <div class="form-text">
                                        <ul>
                                            <li><strong>使用点数计算</strong>: 以实际交易品种实时下单时的价格附加止损止盈点位来计算</li>
                                            <li><strong>使用信号级别</strong>: 使用信号中的R2、S2、MEAN值来配置止损止盈价格 (BUY订单: MEAN为止损、R2为止盈; SELL订单: MEAN为止损、S2为止盈)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 交易对特定配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">交易对配置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <button type="button" id="apply_sl_tp_btn" class="btn btn-warning mb-3">
                                        <i class="bi bi-lightning-charge"></i> 应用止盈止损设置到所有活跃订单
                                    </button>
                                    <div class="alert alert-info" id="apply_sl_tp_result" style="display: none;"></div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mb-3">
                                <strong>交易品种启用状态:</strong> 只有被勾选的交易品种才会执行交易信号。
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">启用交易品种</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex flex-wrap gap-3">
                                                {% for symbol in ['XAUUSD', 'BTCUSD', 'ETHUSD', 'GBPUSD', 'GBPJPY', 'BRENT', 'XTIUSD', 'DXY'] %}
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox"
                                                           id="enable_symbol_{{ symbol }}"
                                                           name="enable_symbol_{{ symbol }}"
                                                           {% if config.enabled_symbols and config.enabled_symbols.get(symbol, True) %}checked{% endif %}>
                                                    <label class="form-check-label" for="enable_symbol_{{ symbol }}">
                                                        {{ symbol }}
                                                    </label>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>交易对</th>
                                            <th>交易量</th>
                                            <th>止损点数</th>
                                            <th>止盈点数</th>
                                            <th>活跃订单数量限制</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for symbol in ['BTCUSD', 'ETHUSD', 'XAUUSD', 'GBPJPY', 'GBPUSD', 'BRENT', 'XTIUSD', 'DXY'] %}
                                            <tr>
                                                <td>{{ symbol }}</td>
                                                <td>
                                                    <input type="number" class="form-control" id="{{ symbol }}_volume" name="{{ symbol }}_volume"
                                                           step="0.01" min="0.01" value="{{ config.symbol_volumes.get(symbol, config.default_volume) }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" id="{{ symbol }}_sl" name="{{ symbol }}_sl"
                                                           step="1" min="1" value="{{ config.symbol_sl_points.get(symbol, config.default_sl_points) }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" id="{{ symbol }}_tp" name="{{ symbol }}_tp"
                                                           step="1" min="1" value="{{ config.symbol_tp_points.get(symbol, config.default_tp_points) }}">
                                                </td>
                                                <td>
                                                    <input type="number" class="form-control" id="{{ symbol }}_max_orders" name="{{ symbol }}_max_orders"
                                                           step="1" min="1" value="{{ config.symbol_max_active_orders.get(symbol, 2) }}" title="该交易对允许的最大活跃订单数量">
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 交易时段配置 -->
                    <div class="card mb-4" id="trading_time_config" {% if not config.enable_time_filter %}style="display: none;"{% endif %}>
                        <div class="card-header">
                            <h5 class="mb-0">交易时段配置</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <strong>时区说明:</strong> 系统现在使用<b>北京时间（GMT+8）</b>进行交易时段过滤。<br />
                                请直接按照北京时间填写交易时段，例如：工作日交易时段设置为9:30-16:30表示北京时间上午9:30到下午4:30。<br />
                                <span class="text-success">所有时间配置均使用北京时间，不需要进行时区转换计算。</span>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>星期</th>
                                            <th>是否交易</th>
                                            <th>时间段1</th>
                                            <th>时间段2</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for day_num, day_name in [
                                            (1, '星期一'), 
                                            (2, '星期二'), 
                                            (3, '星期三'), 
                                            (4, '星期四'), 
                                            (5, '星期五'),
                                            (6, '星期六'),
                                            (0, '星期日')
                                        ] %}
                                        <tr>
                                            <td>{{ day_name }}</td>
                                            <td>
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" 
                                                           id="day_enabled_{{ day_num }}" 
                                                           name="day_enabled_{{ day_num }}" 
                                                           {% if config.trading_times and config.trading_times[day_num|string] and config.trading_times[day_num|string].enabled %}checked{% endif %}>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="time" class="form-control" 
                                                           id="day_{{ day_num }}_period1_start" 
                                                           name="day_{{ day_num }}_period1_start" 
                                                           value="{{ config.trading_times[day_num|string].period1.start if config.trading_times and config.trading_times[day_num|string] and config.trading_times[day_num|string].period1 else '00:00' }}">
                                                    <span class="input-group-text">到</span>
                                                    <input type="time" class="form-control" 
                                                           id="day_{{ day_num }}_period1_end" 
                                                           name="day_{{ day_num }}_period1_end" 
                                                           value="{{ config.trading_times[day_num|string].period1.end if config.trading_times and config.trading_times[day_num|string] and config.trading_times[day_num|string].period1 else '00:00' }}">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="time" class="form-control" 
                                                           id="day_{{ day_num }}_period2_start" 
                                                           name="day_{{ day_num }}_period2_start" 
                                                           value="{{ config.trading_times[day_num|string].period2.start if config.trading_times and config.trading_times[day_num|string] and config.trading_times[day_num|string].period2 else '00:00' }}">
                                                    <span class="input-group-text">到</span>
                                                    <input type="time" class="form-control" 
                                                           id="day_{{ day_num }}_period2_end" 
                                                           name="day_{{ day_num }}_period2_end" 
                                                           value="{{ config.trading_times[day_num|string].period2.end if config.trading_times and config.trading_times[day_num|string] and config.trading_times[day_num|string].period2 else '00:00' }}">
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bark通知配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">通知设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bark_device_key" class="form-label">Bark设备密钥 1</label>
                                    <input type="text" class="form-control" id="bark_device_key" name="bark_device_key" value="{{ config.bark_device_key }}">
                                    <div class="form-text">
                                        主要Bark设备密钥，用于发送通知
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="bark_device_key_2" class="form-label">Bark设备密钥 2 <span class="text-muted">(可选)</span></label>
                                    <input type="text" class="form-control" id="bark_device_key_2" name="bark_device_key_2" value="{{ config.bark_device_key_2 or '' }}">
                                    <div class="form-text">
                                        第二个Bark设备密钥，留空则只发送到设备1
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="bark_sound" class="form-label">通知提示音</label>
                                    <select class="form-select" id="bark_sound" name="bark_sound">
                                        <option value="default" {% if config.bark_sound == 'default' %}selected{% endif %}>默认</option>
                                        <option value="minuet.caf" {% if config.bark_sound == 'minuet.caf' %}selected{% endif %}>Minuet</option>
                                        <option value="chime.caf" {% if config.bark_sound == 'chime.caf' %}selected{% endif %}>Chime</option>
                                        <option value="alarm.caf" {% if config.bark_sound == 'alarm.caf' %}selected{% endif %}>Alarm</option>
                                        <option value="bell.caf" {% if config.bark_sound == 'bell.caf' %}selected{% endif %}>Bell</option>
                                        <option value="telegraph.caf" {% if config.bark_sound == 'telegraph.caf' %}selected{% endif %}>Telegraph</option>
                                        <option value="electronic.caf" {% if config.bark_sound == 'electronic.caf' %}selected{% endif %}>Electronic</option>
                                    </select>
                                    <div class="form-text">
                                        Bark通知声音
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <small>
                                            <i class="bi bi-info-circle"></i>
                                            <strong>多设备通知：</strong>如果配置了两个设备密钥，通知将同时发送到两个设备。如果只填写设备密钥1，则只发送到一个设备。
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="bark_url" class="form-label">Bark服务地址</label>
                                    <input type="text" class="form-control" id="bark_url" name="bark_url" value="{{ config.bark_url }}">
                                    <div class="form-text">Bark通知的API URL</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="bark_title" class="form-label">通知标题</label>
                                    <input type="text" class="form-control" id="bark_title" name="bark_title" value="{{ config.bark_title }}">
                                    <div class="form-text">通知的标题内容</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="bark_level" class="form-label">通知级别</label>
                                    <input type="text" class="form-control" id="bark_level" name="bark_level" value="{{ config.bark_level }}">
                                    <div class="form-text">通知级别（active, time, passively等）</div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="bark_volume" class="form-label">通知音量</label>
                                    <input type="number" class="form-control" id="bark_volume" name="bark_volume" step="0.1" min="0" value="{{ config.bark_volume }}">
                                    <div class="form-text">通知提示音音量（0-1）</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="bark_badge" class="form-label">通知徽章</label>
                                    <input type="number" class="form-control" id="bark_badge" name="bark_badge" step="1" min="0" value="{{ config.bark_badge }}">
                                    <div class="form-text">通知徽章数字</div>
                                </div>
                                <div class="col-md-4">
                                    <label for="call" class="form-label">呼叫模式</label>
                                    <select class="form-select" id="call" name="call">
                                        <option value="0" {% if config.call == '0' %}selected{% endif %}>禁用</option>
                                        <option value="1" {% if config.call == '1' %}selected{% endif %}>启用</option>
                                    </select>
                                    <div class="form-text">是否启用呼叫模式</div>
                                </div>
                            </div>

                            <!-- Bark通知类型选择 -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label">通知类型设置</label>
                                    <div class="alert alert-info">
                                        <small>
                                            <i class="bi bi-info-circle"></i>
                                            选择您希望接收的通知类型。<strong>建议保持"交易执行通知"开启</strong>，以确保及时了解交易情况。
                                        </small>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="notify_signal_received" name="notify_signal_received"
                                                       {% if config.bark_notifications and config.bark_notifications.signal_received %}checked{% endif %}>
                                                <label class="form-check-label" for="notify_signal_received">
                                                    📡 信号接收通知
                                                </label>
                                                <div class="form-text">当系统接收到webhook信号时发送通知</div>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="notify_signal_processing" name="notify_signal_processing"
                                                       {% if config.bark_notifications and config.bark_notifications.signal_processing %}checked{% endif %}>
                                                <label class="form-check-label" for="notify_signal_processing">
                                                    ⚡ 信号处理通知
                                                </label>
                                                <div class="form-text">信号处理状态更新通知（开始/成功/失败）</div>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="notify_trade_execution" name="notify_trade_execution"
                                                       {% if config.bark_notifications and config.bark_notifications.trade_execution %}checked{% endif %}>
                                                <label class="form-check-label" for="notify_trade_execution">
                                                    💰 交易执行通知 <span class="badge bg-warning text-dark">重要</span>
                                                </label>
                                                <div class="form-text">当成功执行交易订单时发送通知（强烈建议开启）</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="notify_trade_closed" name="notify_trade_closed"
                                                       {% if config.bark_notifications and config.bark_notifications.trade_closed %}checked{% endif %}>
                                                <label class="form-check-label" for="notify_trade_closed">
                                                    🔄 平仓通知
                                                </label>
                                                <div class="form-text">当订单平仓时发送通知（包含盈亏信息）</div>
                                            </div>
                                            <div class="form-check form-switch mb-2">
                                                <input class="form-check-input" type="checkbox" id="notify_error" name="notify_error"
                                                       {% if config.bark_notifications and config.bark_notifications.error %}checked{% endif %}>
                                                <label class="form-check-label" for="notify_error">
                                                    ❌ 错误通知
                                                </label>
                                                <div class="form-text">当系统发生错误时发送通知</div>
                                            </div>
                                            <div class="alert alert-warning mt-2">
                                                <small>
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                    <strong>注意：</strong>如果关闭"交易执行通知"，您将无法及时了解交易订单的执行情况！
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bark通知测试区域 -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label">通知测试</label>
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button type="button" class="btn btn-outline-primary" id="testBarkBtn">
                                            <i class="bi bi-bell"></i> 发送测试通知
                                        </button>
                                        <button type="button" class="btn btn-outline-success" id="testTradeNotificationBtn">
                                            <i class="bi bi-graph-up"></i> 测试交易通知
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" id="testErrorNotificationBtn">
                                            <i class="bi bi-exclamation-triangle"></i> 测试错误通知
                                        </button>
                                        <button type="button" class="btn btn-outline-info" id="testSignalNotificationBtn">
                                            <i class="bi bi-broadcast"></i> 测试信号通知
                                        </button>
                                    </div>
                                    <div class="form-text">点击按钮测试Bark通知功能，确保配置正确且设备能正常接收通知</div>
                                    <div id="barkTestResult" class="mt-2" style="display: none;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 余额监测配置 -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">💰 余额监测设置</h5>
                        </div>
                        <div class="card-body">
                            <!-- 总开关 -->
                            <div class="form-check form-switch mb-4">
                                <input class="form-check-input" type="checkbox" id="balance_monitoring_enabled" name="balance_monitoring_enabled"
                                       {% if config.balance_monitoring and config.balance_monitoring.enabled %}checked{% endif %}>
                                <label class="form-check-label" for="balance_monitoring_enabled">
                                    <strong>启用余额监测功能</strong>
                                </label>
                                <div class="form-text">开启后系统将监测账户余额变动并发送相关通知</div>
                            </div>

                            <!-- 余额变动通知 -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h6 class="text-primary">📊 余额变动通知</h6>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="balance_change_enabled" name="balance_change_enabled"
                                               {% if config.balance_monitoring and config.balance_monitoring.balance_change_notification and config.balance_monitoring.balance_change_notification.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="balance_change_enabled">
                                            启用余额变动通知
                                        </label>
                                        <div class="form-text">当账户余额发生变动时发送通知（非浮动盈亏）</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="min_change_amount" class="form-label">最小变动金额 (USD)</label>
                                            <input type="number" class="form-control" id="min_change_amount" name="min_change_amount"
                                                   step="0.1" min="0.1"
                                                   value="{{ config.balance_monitoring.balance_change_notification.min_change_amount if config.balance_monitoring and config.balance_monitoring.balance_change_notification else 1.0 }}">
                                            <div class="form-text">只有变动金额超过此值才会发送通知</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 盈亏通知 -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h6 class="text-success">💹 盈亏通知</h6>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="profit_loss_enabled" name="profit_loss_enabled"
                                               {% if config.balance_monitoring and config.balance_monitoring.profit_loss_notification and config.balance_monitoring.profit_loss_notification.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="profit_loss_enabled">
                                            启用盈亏通知
                                        </label>
                                        <div class="form-text">当盈利或亏损达到设定阈值时发送通知</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="profit_threshold" class="form-label">盈利通知阈值 (USD)</label>
                                            <input type="number" class="form-control" id="profit_threshold" name="profit_threshold"
                                                   step="1" min="1"
                                                   value="{{ config.balance_monitoring.profit_loss_notification.profit_threshold if config.balance_monitoring and config.balance_monitoring.profit_loss_notification else 50.0 }}">
                                            <div class="form-text">盈利达到此金额时发送通知</div>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="loss_threshold" class="form-label">亏损通知阈值 (USD)</label>
                                            <input type="number" class="form-control" id="loss_threshold" name="loss_threshold"
                                                   step="1" min="1"
                                                   value="{{ config.balance_monitoring.profit_loss_notification.loss_threshold if config.balance_monitoring and config.balance_monitoring.profit_loss_notification else 50.0 }}">
                                            <div class="form-text">亏损达到此金额时发送通知</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 定时余额推送 -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h6 class="text-info">⏰ 定时余额推送</h6>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="periodic_balance_enabled" name="periodic_balance_enabled"
                                               {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="periodic_balance_enabled">
                                            启用定时余额推送
                                        </label>
                                        <div class="form-text">24小时全天定期推送账户余额信息，从0点开始计算</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label for="interval_hours" class="form-label">推送间隔 (小时)</label>
                                            <select class="form-select" id="interval_hours" name="interval_hours">
                                                <option value="1" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 1 %}selected{% endif %}>每小时</option>
                                                <option value="2" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 2 %}selected{% endif %}>每2小时</option>
                                                <option value="4" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 4 %}selected{% endif %}>每4小时</option>
                                                <option value="6" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 6 %}selected{% endif %}>每6小时</option>
                                                <option value="8" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 8 %}selected{% elif not config.balance_monitoring or not config.balance_monitoring.periodic_balance_notification %}selected{% endif %}>每8小时</option>
                                                <option value="12" {% if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification and config.balance_monitoring.periodic_balance_notification.interval_hours == 12 %}selected{% endif %}>每12小时</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="start_time" class="form-label">开始时间</label>
                                            <input type="time" class="form-control" id="start_time" name="start_time"
                                                   value="{{ config.balance_monitoring.periodic_balance_notification.start_time if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification else '00:00' }}">
                                            <div class="form-text">每日推送开始时间（默认00:00全天）</div>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="end_time" class="form-label">结束时间</label>
                                            <input type="time" class="form-control" id="end_time" name="end_time"
                                                   value="{{ config.balance_monitoring.periodic_balance_notification.end_time if config.balance_monitoring and config.balance_monitoring.periodic_balance_notification else '23:59' }}">
                                            <div class="form-text">每日推送结束时间（默认23:59全天）</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 余额监测测试 -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label class="form-label">余额监测测试</label>
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button type="button" class="btn btn-outline-primary" id="testBalanceChangeBtn">
                                            <i class="bi bi-graph-up-arrow"></i> 测试余额变动通知
                                        </button>
                                        <button type="button" class="btn btn-outline-success" id="testProfitNotificationBtn">
                                            <i class="bi bi-currency-dollar"></i> 测试盈利通知
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" id="testLossNotificationBtn">
                                            <i class="bi bi-exclamation-diamond"></i> 测试亏损通知
                                        </button>
                                        <button type="button" class="btn btn-outline-info" id="testPeriodicBalanceBtn">
                                            <i class="bi bi-clock"></i> 测试定时余额推送
                                        </button>
                                    </div>
                                    <div class="form-text">点击按钮测试余额监测通知功能</div>
                                    <div id="balanceTestResult" class="mt-2" style="display: none;"></div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i>
                                <strong>说明：</strong>
                                <ul class="mb-0 mt-2">
                                    <li><strong>余额变动通知：</strong>监测实际余额变化（如平仓后的余额变动），不包括浮动盈亏</li>
                                    <li><strong>盈亏通知：</strong>当浮动盈亏达到设定阈值时发送通知</li>
                                    <li><strong>定时推送：</strong>24小时全天运行，从0点开始按指定间隔推送（默认每8小时：0点、8点、16点）</li>
                                    <li><strong>时间限制：</strong>可设置开始和结束时间来限制推送时间范围（默认全天00:00-23:59）</li>
                                    <li><strong>双设备通知：</strong>所有余额监测通知都会发送到两个Bark设备</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                        <button type="submit" class="btn btn-primary">保存设置</button>
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 处理交易时段配置显示/隐藏
            const enableTimeFilterCheckbox = document.getElementById('enable_time_filter');
            const tradingTimeConfig = document.getElementById('trading_time_config');
            
            enableTimeFilterCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    tradingTimeConfig.style.display = 'block';
                } else {
                    tradingTimeConfig.style.display = 'none';
                }
            });
            
            // 实时更新时间显示
            function updateTimes() {
                // 获取MT5服务器时间（这里使用页面加载时的服务器时间，并计算偏移量）
                const serverTimeEl = document.getElementById('mt5-server-time');
                const initialServerTime = new Date(serverTimeEl.textContent);
                const initialLocalTime = new Date();
                const timeDiff = initialServerTime - initialLocalTime;
                
                // 更新函数
                function updateDisplayedTimes() {
                    const now = new Date();
                    
                    // 更新MT5服务器时间（本地时间+时差）
                    const currentServerTime = new Date(now.getTime() + timeDiff);
                    serverTimeEl.textContent = currentServerTime.toLocaleString();
                    
                    // 获取北京时间（从服务器获取真实北京时间）
                    fetch('/api/beijing_time')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                document.getElementById('beijing-time').textContent = data.beijing_time;
                            }
                        })
                        .catch(error => console.error('获取北京时间失败:', error));
                }
                
                // 立即更新一次
                updateDisplayedTimes();
                
                // 每秒更新一次
                setInterval(updateDisplayedTimes, 1000);
            }
            
            updateTimes();

            // 处理MT5环境检查按钮点击事件
            const checkMT5EnvironmentBtn = document.getElementById('checkMT5EnvironmentBtn');
            const mt5EnvironmentResult = document.getElementById('mt5EnvironmentResult');

            checkMT5EnvironmentBtn.addEventListener('click', async function() {
                try {
                    // 显示加载状态
                    checkMT5EnvironmentBtn.disabled = true;
                    checkMT5EnvironmentBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 检查中...';
                    mt5EnvironmentResult.style.display = 'none';

                    // 调用环境检查API
                    const response = await fetch('/api/check_mt5_environment');
                    const result = await response.json();

                    // 显示结果
                    mt5EnvironmentResult.style.display = 'block';

                    if (result.success) {
                        let statusClass = 'alert-info';
                        let statusIcon = 'bi-info-circle';
                        let statusText = '环境检查完成';

                        let details = '<div class="mt-2"><strong>检查结果:</strong><ul>';

                        if (result.mt5_found) {
                            details += '<li class="text-success"><i class="bi bi-check-circle"></i> MT5已安装</li>';
                            if (result.mt5_paths && result.mt5_paths.length > 0) {
                                details += '<li>找到的MT5路径:<ul>';
                                result.mt5_paths.forEach(path => {
                                    details += `<li><code>${path}</code></li>`;
                                });
                                details += '</ul></li>';
                            }
                        } else {
                            details += '<li class="text-danger"><i class="bi bi-x-circle"></i> 未找到MT5安装</li>';
                            statusClass = 'alert-warning';
                            statusIcon = 'bi-exclamation-triangle';
                            statusText = '未找到MT5安装';
                        }

                        if (result.mt5_running) {
                            details += '<li class="text-success"><i class="bi bi-check-circle"></i> MT5进程正在运行</li>';
                        } else {
                            details += '<li class="text-warning"><i class="bi bi-exclamation-triangle"></i> MT5进程未运行</li>';
                        }

                        if (result.python_mt5_available) {
                            details += '<li class="text-success"><i class="bi bi-check-circle"></i> Python MT5库可用</li>';
                        } else {
                            details += '<li class="text-danger"><i class="bi bi-x-circle"></i> Python MT5库不可用</li>';
                        }

                        details += '</ul></div>';

                        mt5EnvironmentResult.className = `alert ${statusClass}`;
                        mt5EnvironmentResult.innerHTML = `<strong><i class="${statusIcon}"></i> ${statusText}</strong>${details}`;
                    } else {
                        mt5EnvironmentResult.className = 'alert alert-danger';
                        mt5EnvironmentResult.innerHTML = `<strong><i class="bi bi-x-circle"></i> 检查失败!</strong> ${result.error}`;
                    }
                } catch (error) {
                    console.error('检查MT5环境时发生错误:', error);
                    mt5EnvironmentResult.style.display = 'block';
                    mt5EnvironmentResult.className = 'alert alert-danger';
                    mt5EnvironmentResult.innerHTML = `<strong>错误!</strong> 检查环境时发生错误: ${error.message}`;
                } finally {
                    // 恢复按钮状态
                    checkMT5EnvironmentBtn.disabled = false;
                    checkMT5EnvironmentBtn.innerHTML = '<i class="bi bi-search"></i> 检查环境';
                }
            });

            // 处理MT5连接测试按钮点击事件
            const testMT5ConnectionBtn = document.getElementById('testMT5ConnectionBtn');
            const mt5TestResult = document.getElementById('mt5TestResult');

            testMT5ConnectionBtn.addEventListener('click', async function() {
                try {
                    // 获取表单数据
                    const mt5Login = document.getElementById('mt5_login').value.trim();
                    const mt5Password = document.getElementById('mt5_password').value.trim();
                    const mt5Server = document.getElementById('mt5_server').value.trim();
                    const mt5Path = document.getElementById('mt5_path').value.trim();

                    // 验证必填字段
                    if (!mt5Login || !mt5Password || !mt5Server) {
                        mt5TestResult.style.display = 'block';
                        mt5TestResult.className = 'alert alert-danger';
                        mt5TestResult.innerHTML = '<strong>错误!</strong> 请填写完整的MT5连接信息';
                        return;
                    }

                    // 显示加载状态
                    testMT5ConnectionBtn.disabled = true;
                    testMT5ConnectionBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 测试中...';
                    mt5TestResult.style.display = 'none';

                    // 调用测试API
                    const response = await fetch('/api/test_mt5_connection', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            mt5_login: mt5Login,
                            mt5_password: mt5Password,
                            mt5_server: mt5Server,
                            mt5_path: mt5Path
                        })
                    });

                    const result = await response.json();

                    // 显示结果
                    mt5TestResult.style.display = 'block';

                    if (result.success) {
                        mt5TestResult.className = 'alert alert-success';
                        let accountInfo = '';
                        if (result.account_info) {
                            accountInfo = `
                                <div class="mt-2">
                                    <strong>账户信息:</strong>
                                    <ul class="mb-0">
                                        <li>账号: ${result.account_info.login}</li>
                                        <li>服务器: ${result.account_info.server}</li>
                                        <li>账户名: ${result.account_info.name}</li>
                                        <li>余额: ${result.account_info.balance} ${result.account_info.currency}</li>
                                    </ul>
                                </div>
                            `;
                        }
                        mt5TestResult.innerHTML = `<strong><i class="bi bi-check-circle"></i> 连接成功!</strong> ${result.message}${accountInfo}`;
                    } else {
                        mt5TestResult.className = 'alert alert-danger';
                        let errorDetails = '';
                        if (result.error_code) {
                            errorDetails = `<div class="mt-2"><small><strong>错误代码:</strong> ${result.error_code}</small></div>`;
                        }
                        // 将换行符转换为HTML换行
                        const formattedError = result.error.replace(/\n/g, '<br>');
                        mt5TestResult.innerHTML = `<strong><i class="bi bi-x-circle"></i> 连接失败!</strong><br>${formattedError}${errorDetails}`;
                    }
                } catch (error) {
                    console.error('测试MT5连接时发生错误:', error);
                    mt5TestResult.style.display = 'block';
                    mt5TestResult.className = 'alert alert-danger';
                    mt5TestResult.innerHTML = `<strong>错误!</strong> 测试连接时发生错误: ${error.message}`;
                } finally {
                    // 恢复按钮状态
                    testMT5ConnectionBtn.disabled = false;
                    testMT5ConnectionBtn.innerHTML = '<i class="bi bi-wifi"></i> 测试连接';
                }
            });

            // 处理应用止盈止损按钮点击事件
            const applySLTPBtn = document.getElementById('apply_sl_tp_btn');
            const applySLTPResult = document.getElementById('apply_sl_tp_result');
            
            applySLTPBtn.addEventListener('click', async function() {
                try {
                    // 显示加载状态
                    applySLTPBtn.disabled = true;
                    applySLTPBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 应用中...';
                    applySLTPResult.style.display = 'none';
                    
                    // 调用API
                    const response = await fetch('/api/apply_sl_tp_to_all', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const result = await response.json();
                    
                    // 显示结果
                    applySLTPResult.style.display = 'block';
                    
                    if (result.success) {
                        applySLTPResult.className = 'alert alert-success';
                        let detailsHtml = '';
                        
                        if (result.details && result.details.length > 0) {
                            detailsHtml = '<div class="mt-2"><strong>详情:</strong><ul>';
                            result.details.forEach(detail => {
                                const alertClass = detail.success ? 'text-success' : 'text-danger';
                                detailsHtml += `<li class="${alertClass}">#${detail.ticket} (${detail.symbol}): ${detail.message}</li>`;
                            });
                            detailsHtml += '</ul></div>';
                        }
                        
                        applySLTPResult.innerHTML = `<strong>成功!</strong> ${result.message}${detailsHtml}`;
                    } else {
                        applySLTPResult.className = 'alert alert-danger';
                        applySLTPResult.innerHTML = `<strong>失败!</strong> ${result.message}`;
                    }
                } catch (error) {
                    console.error('应用止盈止损设置时发生错误:', error);
                    applySLTPResult.style.display = 'block';
                    applySLTPResult.className = 'alert alert-danger';
                    applySLTPResult.innerHTML = `<strong>错误!</strong> 应用止盈止损设置时发生错误: ${error.message}`;
                } finally {
                    // 恢复按钮状态
                    applySLTPBtn.disabled = false;
                    applySLTPBtn.innerHTML = '<i class="bi bi-lightning-charge"></i> 应用止盈止损设置到所有活跃订单';
                }
            });
            
            // 处理交易品种启用状态变更
            document.querySelectorAll('input[id^="enable_symbol_"]').forEach(checkbox => {
                checkbox.addEventListener('change', async function() {
                    const symbolName = this.id.replace('enable_symbol_', '');
                    const isEnabled = this.checked;
                    
                    try {
                        // 显示临时提示
                        const symbolLabel = this.nextElementSibling;
                        const originalText = symbolLabel.textContent;
                        symbolLabel.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ${symbolName} 更新中...`;
                        
                        // 调用API更新状态
                        const response = await fetch('/api/update_symbol_status', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                symbol: symbolName,
                                enabled: isEnabled
                            })
                        });
                        
                        const result = await response.json();
                        
                        // 恢复标签文本
                        symbolLabel.textContent = originalText;
                        
                        if (!result.success) {
                            console.error('更新交易品种状态失败:', result.message);
                            // 如果更新失败，恢复复选框状态
                            this.checked = !isEnabled;
                            
                            // 显示错误提示
                            alert(`更新${symbolName}状态失败: ${result.message}`);
                        } else {
                            console.log(`交易品种 ${symbolName} 已${isEnabled ? '启用' : '禁用'}`);
                        }
                    } catch (error) {
                        console.error('更新交易品种状态时出错:', error);
                        // 恢复复选框状态
                        this.checked = !isEnabled;
                        alert(`更新${symbolName}状态时出错: ${error.message}`);
                    }
                });
            });

            // Bark通知测试功能
            const testBarkBtn = document.getElementById('testBarkBtn');
            const testTradeNotificationBtn = document.getElementById('testTradeNotificationBtn');
            const testErrorNotificationBtn = document.getElementById('testErrorNotificationBtn');
            const testSignalNotificationBtn = document.getElementById('testSignalNotificationBtn');
            const barkTestResult = document.getElementById('barkTestResult');

            // 显示测试结果
            function showTestResult(success, message, details = null) {
                barkTestResult.style.display = 'block';
                barkTestResult.className = success ? 'alert alert-success' : 'alert alert-danger';

                let content = `<strong>${success ? '成功!' : '失败!'}</strong> ${message}`;
                if (details) {
                    content += `<div class="mt-2"><small>${details}</small></div>`;
                }
                barkTestResult.innerHTML = content;

                // 5秒后自动隐藏
                setTimeout(() => {
                    barkTestResult.style.display = 'none';
                }, 5000);
            }

            // 基础测试通知
            testBarkBtn.addEventListener('click', async function() {
                try {
                    testBarkBtn.disabled = true;
                    testBarkBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                    const response = await fetch('/api/test_bark_notification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'basic'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showTestResult(true, result.message, result.details);
                    } else {
                        showTestResult(false, result.message);
                    }
                } catch (error) {
                    showTestResult(false, '发送测试通知时发生错误', error.message);
                } finally {
                    testBarkBtn.disabled = false;
                    testBarkBtn.innerHTML = '<i class="bi bi-bell"></i> 发送测试通知';
                }
            });

            // 交易通知测试
            testTradeNotificationBtn.addEventListener('click', async function() {
                try {
                    testTradeNotificationBtn.disabled = true;
                    testTradeNotificationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                    const response = await fetch('/api/test_bark_notification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'trade'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showTestResult(true, result.message, result.details);
                    } else {
                        showTestResult(false, result.message);
                    }
                } catch (error) {
                    showTestResult(false, '发送交易测试通知时发生错误', error.message);
                } finally {
                    testTradeNotificationBtn.disabled = false;
                    testTradeNotificationBtn.innerHTML = '<i class="bi bi-graph-up"></i> 测试交易通知';
                }
            });

            // 错误通知测试
            testErrorNotificationBtn.addEventListener('click', async function() {
                try {
                    testErrorNotificationBtn.disabled = true;
                    testErrorNotificationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                    const response = await fetch('/api/test_bark_notification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'error'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showTestResult(true, result.message, result.details);
                    } else {
                        showTestResult(false, result.message);
                    }
                } catch (error) {
                    showTestResult(false, '发送错误测试通知时发生错误', error.message);
                } finally {
                    testErrorNotificationBtn.disabled = false;
                    testErrorNotificationBtn.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 测试错误通知';
                }
            });

            // 信号通知测试
            testSignalNotificationBtn.addEventListener('click', async function() {
                try {
                    testSignalNotificationBtn.disabled = true;
                    testSignalNotificationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                    const response = await fetch('/api/test_bark_notification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            type: 'signal'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showTestResult(true, result.message, result.details);
                    } else {
                        showTestResult(false, result.message);
                    }
                } catch (error) {
                    showTestResult(false, '发送信号测试通知时发生错误', error.message);
                } finally {
                    testSignalNotificationBtn.disabled = false;
                    testSignalNotificationBtn.innerHTML = '<i class="bi bi-broadcast"></i> 测试信号通知';
                }
            });

            // 余额监测测试功能
            const testBalanceChangeBtn = document.getElementById('testBalanceChangeBtn');
            const testProfitNotificationBtn = document.getElementById('testProfitNotificationBtn');
            const testLossNotificationBtn = document.getElementById('testLossNotificationBtn');
            const testPeriodicBalanceBtn = document.getElementById('testPeriodicBalanceBtn');
            const balanceTestResult = document.getElementById('balanceTestResult');

            function showBalanceTestResult(success, message, details = '') {
                balanceTestResult.style.display = 'block';
                balanceTestResult.className = `alert ${success ? 'alert-success' : 'alert-danger'}`;
                balanceTestResult.innerHTML = `
                    <strong>${success ? '成功' : '失败'}:</strong> ${message}
                    ${details ? `<br><small>${details}</small>` : ''}
                `;
            }

            // 余额变动通知测试
            if (testBalanceChangeBtn) {
                testBalanceChangeBtn.addEventListener('click', async function() {
                    try {
                        testBalanceChangeBtn.disabled = true;
                        testBalanceChangeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                        const response = await fetch('/api/test_balance_notification', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                type: 'balance_change'
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showBalanceTestResult(true, result.message, result.details);
                        } else {
                            showBalanceTestResult(false, result.message);
                        }
                    } catch (error) {
                        showBalanceTestResult(false, '发送余额变动测试通知时发生错误', error.message);
                    } finally {
                        testBalanceChangeBtn.disabled = false;
                        testBalanceChangeBtn.innerHTML = '<i class="bi bi-graph-up-arrow"></i> 测试余额变动通知';
                    }
                });
            }

            // 盈利通知测试
            if (testProfitNotificationBtn) {
                testProfitNotificationBtn.addEventListener('click', async function() {
                    try {
                        testProfitNotificationBtn.disabled = true;
                        testProfitNotificationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                        const response = await fetch('/api/test_balance_notification', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                type: 'profit'
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showBalanceTestResult(true, result.message, result.details);
                        } else {
                            showBalanceTestResult(false, result.message);
                        }
                    } catch (error) {
                        showBalanceTestResult(false, '发送盈利测试通知时发生错误', error.message);
                    } finally {
                        testProfitNotificationBtn.disabled = false;
                        testProfitNotificationBtn.innerHTML = '<i class="bi bi-currency-dollar"></i> 测试盈利通知';
                    }
                });
            }

            // 亏损通知测试
            if (testLossNotificationBtn) {
                testLossNotificationBtn.addEventListener('click', async function() {
                    try {
                        testLossNotificationBtn.disabled = true;
                        testLossNotificationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                        const response = await fetch('/api/test_balance_notification', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                type: 'loss'
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showBalanceTestResult(true, result.message, result.details);
                        } else {
                            showBalanceTestResult(false, result.message);
                        }
                    } catch (error) {
                        showBalanceTestResult(false, '发送亏损测试通知时发生错误', error.message);
                    } finally {
                        testLossNotificationBtn.disabled = false;
                        testLossNotificationBtn.innerHTML = '<i class="bi bi-exclamation-diamond"></i> 测试亏损通知';
                    }
                });
            }

            // 定时余额推送测试
            if (testPeriodicBalanceBtn) {
                testPeriodicBalanceBtn.addEventListener('click', async function() {
                    try {
                        testPeriodicBalanceBtn.disabled = true;
                        testPeriodicBalanceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 发送中...';

                        const response = await fetch('/api/test_balance_notification', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                type: 'periodic'
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showBalanceTestResult(true, result.message, result.details);
                        } else {
                            showBalanceTestResult(false, result.message);
                        }
                    } catch (error) {
                        showBalanceTestResult(false, '发送定时余额推送测试通知时发生错误', error.message);
                    } finally {
                        testPeriodicBalanceBtn.disabled = false;
                        testPeriodicBalanceBtn.innerHTML = '<i class="bi bi-clock"></i> 测试定时余额推送';
                    }
                });
            }
        });
    </script>
</body>
</html>
