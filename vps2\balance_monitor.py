#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
余额监测模块
功能：
1. 监测余额变动通知
2. 盈亏通知（达到设定阈值时）
3. 定时推送余额信息
"""

import json
import time
import threading
import logging
from datetime import datetime, timedelta
import sqlite3
import mt5_trader
import bark_notifier

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BalanceMonitor:
    def __init__(self):
        self.config = {}
        self.last_balance = None
        self.last_equity = None
        self.last_profit = None
        self.monitoring_thread = None
        self.periodic_thread = None
        self.running = False
        self.last_periodic_notification = None
        
        # 加载配置
        self.load_config()
        
        # 初始化数据库
        self.init_database()
    
    def load_config(self):
        """加载配置"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            logger.info("余额监测配置加载成功")
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            self.config = {}
    
    def init_database(self):
        """初始化数据库表"""
        try:
            conn = sqlite3.connect('trading_data.db')
            c = conn.cursor()
            
            # 创建余额历史表
            c.execute('''
                CREATE TABLE IF NOT EXISTS balance_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    balance REAL NOT NULL,
                    equity REAL NOT NULL,
                    profit REAL NOT NULL,
                    margin REAL,
                    free_margin REAL,
                    margin_level REAL,
                    change_amount REAL,
                    change_type TEXT,
                    notification_sent INTEGER DEFAULT 0
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("余额监测数据库初始化完成")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def get_account_info(self):
        """获取账户信息"""
        try:
            # 确保MT5连接
            if not mt5_trader.init_mt5():
                logger.error("MT5连接失败")
                return None
            
            account_info = mt5_trader.get_account_info()
            if account_info:
                return {
                    'balance': account_info.get('balance', 0),
                    'equity': account_info.get('equity', 0),
                    'profit': account_info.get('profit', 0),
                    'margin': account_info.get('margin', 0),
                    'free_margin': account_info.get('free_margin', 0),
                    'margin_level': account_info.get('margin_level', 0)
                }
            return None
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return None
    
    def save_balance_record(self, account_info, change_amount=0, change_type="", notification_sent=False):
        """保存余额记录"""
        try:
            conn = sqlite3.connect('trading_data.db')
            c = conn.cursor()
            
            c.execute('''
                INSERT INTO balance_history 
                (timestamp, balance, equity, profit, margin, free_margin, margin_level, 
                 change_amount, change_type, notification_sent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                account_info['balance'],
                account_info['equity'],
                account_info['profit'],
                account_info['margin'],
                account_info['free_margin'],
                account_info['margin_level'],
                change_amount,
                change_type,
                1 if notification_sent else 0
            ))
            
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"保存余额记录失败: {e}")
    
    def check_balance_change(self, current_info):
        """检查余额变动"""
        if self.last_balance is None:
            # 首次运行，记录初始值
            self.last_balance = current_info['balance']
            self.last_equity = current_info['equity']
            self.last_profit = current_info['profit']
            self.save_balance_record(current_info, 0, "初始记录")
            return
        
        # 检查余额变动
        balance_change = current_info['balance'] - self.last_balance
        equity_change = current_info['equity'] - self.last_equity
        profit_change = current_info['profit'] - self.last_profit
        
        balance_config = self.config.get('balance_monitoring', {})
        change_config = balance_config.get('balance_change_notification', {})
        
        # 余额变动通知
        if (change_config.get('enabled', True) and 
            abs(balance_change) >= change_config.get('min_change_amount', 1.0)):
            
            self.send_balance_change_notification(current_info, balance_change)
            self.save_balance_record(current_info, balance_change, "余额变动", True)
        
        # 盈亏通知
        self.check_profit_loss_notification(current_info, profit_change)
        
        # 更新记录的值
        self.last_balance = current_info['balance']
        self.last_equity = current_info['equity']
        self.last_profit = current_info['profit']
    
    def check_profit_loss_notification(self, current_info, profit_change):
        """检查盈亏通知"""
        balance_config = self.config.get('balance_monitoring', {})
        pl_config = balance_config.get('profit_loss_notification', {})
        
        if not pl_config.get('enabled', True):
            return
        
        profit_threshold = pl_config.get('profit_threshold', 50.0)
        loss_threshold = pl_config.get('loss_threshold', 50.0)
        
        # 检查盈利通知
        if profit_change >= profit_threshold:
            self.send_profit_notification(current_info, profit_change)
        
        # 检查亏损通知
        elif profit_change <= -loss_threshold:
            self.send_loss_notification(current_info, abs(profit_change))
    
    def send_balance_change_notification(self, account_info, change_amount):
        """发送余额变动通知"""
        try:
            change_type = "增加" if change_amount > 0 else "减少"
            change_emoji = "📈" if change_amount > 0 else "📉"
            
            title = f"{change_emoji} 余额变动通知"
            
            body = f"""余额发生变动

当前余额: ${account_info['balance']:.2f}
变动金额: {change_amount:+.2f} USD
变动类型: {change_type}

当前净值: ${account_info['equity']:.2f}
浮动盈亏: {account_info['profit']:+.2f} USD
可用保证金: ${account_info['free_margin']:.2f}

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # 发送到两个Bark设备
            result1 = bark_notifier.send_bark_notification(title, body, notification_type="balance_change")
            result2 = bark_notifier.send_bark_notification(title, body, notification_type="balance_change", use_device_2=True)
            
            if result1 or result2:
                logger.info(f"余额变动通知发送成功: 变动{change_amount:+.2f} USD")
            else:
                logger.warning("余额变动通知发送失败")
                
        except Exception as e:
            logger.error(f"发送余额变动通知失败: {e}")
    
    def send_profit_notification(self, account_info, profit_amount):
        """发送盈利通知"""
        try:
            title = "💰 盈利通知"
            
            body = f"""账户盈利达到阈值

盈利金额: +${profit_amount:.2f}
当前余额: ${account_info['balance']:.2f}
当前净值: ${account_info['equity']:.2f}
总浮动盈亏: {account_info['profit']:+.2f} USD

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # 发送到两个Bark设备
            result1 = bark_notifier.send_bark_notification(title, body, notification_type="profit_loss")
            result2 = bark_notifier.send_bark_notification(title, body, notification_type="profit_loss", use_device_2=True)
            
            if result1 or result2:
                logger.info(f"盈利通知发送成功: +${profit_amount:.2f}")
            else:
                logger.warning("盈利通知发送失败")
                
        except Exception as e:
            logger.error(f"发送盈利通知失败: {e}")
    
    def send_loss_notification(self, account_info, loss_amount):
        """发送亏损通知"""
        try:
            title = "⚠️ 亏损通知"
            
            body = f"""账户亏损达到阈值

亏损金额: -${loss_amount:.2f}
当前余额: ${account_info['balance']:.2f}
当前净值: ${account_info['equity']:.2f}
总浮动盈亏: {account_info['profit']:+.2f} USD

时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # 发送到两个Bark设备
            result1 = bark_notifier.send_bark_notification(title, body, notification_type="profit_loss")
            result2 = bark_notifier.send_bark_notification(title, body, notification_type="profit_loss", use_device_2=True)
            
            if result1 or result2:
                logger.info(f"亏损通知发送成功: -${loss_amount:.2f}")
            else:
                logger.warning("亏损通知发送失败")
                
        except Exception as e:
            logger.error(f"发送亏损通知失败: {e}")
    
    def send_periodic_balance_notification(self):
        """发送定时余额通知"""
        try:
            account_info = self.get_account_info()
            if not account_info:
                logger.warning("无法获取账户信息，跳过定时通知")
                return
            
            title = "📊 定时余额报告"
            
            # 计算今日盈亏
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            daily_change = self.get_daily_balance_change(today_start)
            
            body = f"""账户余额定时报告

💰 当前余额: ${account_info['balance']:.2f}
📈 当前净值: ${account_info['equity']:.2f}
💹 浮动盈亏: {account_info['profit']:+.2f} USD
💳 可用保证金: ${account_info['free_margin']:.2f}
📊 保证金比例: {account_info['margin_level']:.1f}%

📅 今日变动: {daily_change:+.2f} USD

⏰ 报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # 发送到两个Bark设备
            result1 = bark_notifier.send_bark_notification(title, body, notification_type="periodic_balance")
            result2 = bark_notifier.send_bark_notification(title, body, notification_type="periodic_balance", use_device_2=True)
            
            if result1 or result2:
                logger.info("定时余额通知发送成功")
                self.last_periodic_notification = datetime.now()
            else:
                logger.warning("定时余额通知发送失败")
                
        except Exception as e:
            logger.error(f"发送定时余额通知失败: {e}")
    
    def get_daily_balance_change(self, start_time):
        """获取今日余额变动"""
        try:
            conn = sqlite3.connect('trading_data.db')
            c = conn.cursor()
            
            c.execute('''
                SELECT balance FROM balance_history 
                WHERE timestamp >= ? 
                ORDER BY timestamp ASC 
                LIMIT 1
            ''', (start_time.isoformat(),))
            
            result = c.fetchone()
            conn.close()
            
            if result and self.last_balance:
                return self.last_balance - result[0]
            return 0
            
        except Exception as e:
            logger.error(f"获取今日余额变动失败: {e}")
            return 0

    def should_send_periodic_notification(self):
        """检查是否应该发送定时通知（24小时制，从0点开始计算）"""
        balance_config = self.config.get('balance_monitoring', {})
        periodic_config = balance_config.get('periodic_balance_notification', {})

        if not periodic_config.get('enabled', True):
            return False

        now = datetime.now()
        interval_hours = periodic_config.get('interval_hours', 8)

        # 获取时间范围配置（用于用户自定义限制）
        start_time_str = periodic_config.get('start_time', '00:00')
        end_time_str = periodic_config.get('end_time', '23:59')

        # 检查是否在用户设定的时间范围内
        try:
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            current_time = now.time()

            # 处理跨天的情况（如22:00-06:00）
            if start_time <= end_time:
                # 同一天内的时间范围
                if not (start_time <= current_time <= end_time):
                    return False
            else:
                # 跨天的时间范围
                if not (current_time >= start_time or current_time <= end_time):
                    return False
        except:
            logger.warning("时间格式错误，使用24小时全天推送")

        # 如果是首次运行，检查是否到了下一个推送时间点
        if self.last_periodic_notification is None:
            # 计算从今天0点开始的下一个推送时间点
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            hours_since_midnight = (now - today_start).total_seconds() / 3600

            # 计算下一个推送时间点（从0点开始，每interval_hours小时一次）
            next_push_hour = ((int(hours_since_midnight) // interval_hours) + 1) * interval_hours

            # 如果当前时间已经过了今天的最后一个推送时间点，等待明天的第一个推送
            if next_push_hour >= 24:
                return False

            # 检查是否到了推送时间（允许1分钟的误差）
            target_time = today_start + timedelta(hours=next_push_hour)
            time_diff = abs((now - target_time).total_seconds())

            return time_diff <= 60  # 1分钟内认为到了推送时间

        # 如果不是首次运行，检查距离上次推送是否已经过了间隔时间
        time_diff = now - self.last_periodic_notification
        return time_diff.total_seconds() >= interval_hours * 3600

    def monitoring_loop(self):
        """监测循环"""
        logger.info("余额监测循环开始")

        while self.running:
            try:
                # 获取当前账户信息
                account_info = self.get_account_info()
                if account_info:
                    # 检查余额变动
                    self.check_balance_change(account_info)

                    # 检查是否需要发送定时通知
                    if self.should_send_periodic_notification():
                        self.send_periodic_balance_notification()

                # 等待30秒后再次检查
                time.sleep(30)

            except Exception as e:
                logger.error(f"监测循环出错: {e}")
                time.sleep(60)  # 出错时等待更长时间

    def start_monitoring(self):
        """开始监测"""
        if self.running:
            logger.warning("余额监测已在运行")
            return

        balance_config = self.config.get('balance_monitoring', {})
        if not balance_config.get('enabled', True):
            logger.info("余额监测功能已禁用")
            return

        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logger.info("余额监测服务已启动")

    def stop_monitoring(self):
        """停止监测"""
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("余额监测服务已停止")

    def test_notifications(self):
        """测试所有通知功能"""
        logger.info("开始测试余额监测通知功能")

        # 获取当前账户信息
        account_info = self.get_account_info()
        if not account_info:
            logger.error("无法获取账户信息，测试失败")
            return False

        try:
            # 测试余额变动通知
            logger.info("测试余额变动通知...")
            self.send_balance_change_notification(account_info, 10.50)

            time.sleep(2)

            # 测试盈利通知
            logger.info("测试盈利通知...")
            self.send_profit_notification(account_info, 75.25)

            time.sleep(2)

            # 测试亏损通知
            logger.info("测试亏损通知...")
            self.send_loss_notification(account_info, 60.80)

            time.sleep(2)

            # 测试定时余额通知
            logger.info("测试定时余额通知...")
            self.send_periodic_balance_notification()

            logger.info("所有余额监测通知测试完成")
            return True

        except Exception as e:
            logger.error(f"测试通知功能失败: {e}")
            return False

    def get_balance_history(self, days=7):
        """获取余额历史记录"""
        try:
            conn = sqlite3.connect('trading_data.db')
            conn.row_factory = sqlite3.Row
            c = conn.cursor()

            start_date = datetime.now() - timedelta(days=days)

            c.execute('''
                SELECT * FROM balance_history
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            ''', (start_date.isoformat(),))

            records = c.fetchall()
            conn.close()

            return [dict(record) for record in records]

        except Exception as e:
            logger.error(f"获取余额历史失败: {e}")
            return []

    def get_monitoring_status(self):
        """获取监测状态"""
        balance_config = self.config.get('balance_monitoring', {})

        return {
            'enabled': balance_config.get('enabled', True),
            'running': self.running,
            'last_balance': self.last_balance,
            'last_equity': self.last_equity,
            'last_profit': self.last_profit,
            'last_periodic_notification': self.last_periodic_notification.isoformat() if self.last_periodic_notification else None,
            'balance_change_notification': balance_config.get('balance_change_notification', {}),
            'profit_loss_notification': balance_config.get('profit_loss_notification', {}),
            'periodic_balance_notification': balance_config.get('periodic_balance_notification', {})
        }

# 全局实例
balance_monitor = BalanceMonitor()

def start_balance_monitoring():
    """启动余额监测服务"""
    balance_monitor.start_monitoring()

def stop_balance_monitoring():
    """停止余额监测服务"""
    balance_monitor.stop_monitoring()

def test_balance_notifications():
    """测试余额通知功能"""
    return balance_monitor.test_notifications()

def get_balance_monitoring_status():
    """获取余额监测状态"""
    return balance_monitor.get_monitoring_status()

def get_balance_history(days=7):
    """获取余额历史"""
    return balance_monitor.get_balance_history(days)

if __name__ == "__main__":
    # 测试模式
    print("余额监测模块测试")

    # 测试通知功能
    if balance_monitor.test_notifications():
        print("✅ 通知功能测试成功")
    else:
        print("❌ 通知功能测试失败")

    # 启动监测（测试模式下运行10秒）
    balance_monitor.start_monitoring()
    time.sleep(10)
    balance_monitor.stop_monitoring()

    print("测试完成")
