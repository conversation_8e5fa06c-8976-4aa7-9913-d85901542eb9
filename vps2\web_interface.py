#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web界面模块 - VPS2的管理后台
"""

import os, sys, logging, json, sqlite3, time, threading
from datetime import datetime, timedelta
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, Response
import MetaTrader5 as mt5
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
import mt5_trader
from api_routes import api
import floating_pl_monitor
import random
import string
from PIL import Image, ImageDraw, ImageFont
import io
import base64

# 日志配置
os.makedirs('logs', exist_ok=True)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
file_handler = logging.FileHandler('logs/web_interface.log', encoding='utf-8')
file_handler.setLevel(logging.DEBUG)
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.DEBUG)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 载入配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    WEB_PORT = config.get('web_port', 8080)
    ADMIN_USERNAME = config.get('admin_username', 'admin')
    ADMIN_PASSWORD = config.get('admin_password', '668899asd')
    
    # 配置日志级别
    log_level = config.get('log_level', 'DEBUG')
    numeric_level = getattr(logging, log_level.upper(), None)
    if isinstance(numeric_level, int):
        logging.getLogger().setLevel(numeric_level)
    
    logger.info(f"配置加载成功，Web端口: {WEB_PORT}")
except Exception as e:
    logger.error(f"加载配置文件失败: {e}", exc_info=True)
    WEB_PORT = 8080
    ADMIN_USERNAME = 'admin'
    ADMIN_PASSWORD = '668899asd'

app = Flask(__name__)
app.secret_key = os.urandom(24)
app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24小时会话有效期

# 注册API Blueprint
app.register_blueprint(api, url_prefix='/api')

# 数据库连接
def get_db_connection():
    """获取数据库连接"""
    try:
        conn = sqlite3.connect('trading_data.db')
        conn.row_factory = sqlite3.Row  # 以字典形式返回结果
        return conn
    except sqlite3.Error as e:
        logger.error(f"数据库连接失败: {e}", exc_info=True)
        raise

# 初始化登录安全相关的数据库表
def init_login_security_tables():
    """初始化登录安全相关的数据库表"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        # 创建登录尝试记录表
        c.execute('''CREATE TABLE IF NOT EXISTS login_attempts
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      ip_address TEXT NOT NULL,
                      username TEXT,
                      success INTEGER DEFAULT 0,
                      attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                      user_agent TEXT)''')

        # 创建验证码会话表
        c.execute('''CREATE TABLE IF NOT EXISTS captcha_sessions
                     (id INTEGER PRIMARY KEY AUTOINCREMENT,
                      session_id TEXT NOT NULL,
                      captcha_code TEXT NOT NULL,
                      created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                      used INTEGER DEFAULT 0)''')

        # 创建索引以提高查询性能
        c.execute('CREATE INDEX IF NOT EXISTS idx_login_attempts_ip_time ON login_attempts(ip_address, attempt_time)')
        c.execute('CREATE INDEX IF NOT EXISTS idx_captcha_sessions_id ON captcha_sessions(session_id)')

        conn.commit()
        conn.close()
        logger.info("登录安全数据库表初始化成功")
    except sqlite3.Error as e:
        logger.error(f"登录安全数据库表初始化失败: {e}", exc_info=True)

# 验证码相关函数
def generate_captcha_code(length=4):
    """生成验证码字符串"""
    # 使用数字和大写字母，避免容易混淆的字符
    chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
    return ''.join(random.choice(chars) for _ in range(length))

def create_captcha_image(code):
    """创建验证码图片"""
    try:
        # 图片尺寸
        width, height = 120, 40

        # 创建图片
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)

        # 尝试使用系统字体，如果失败则使用默认字体
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 20)
            except:
                font = ImageFont.load_default()

        # 绘制验证码文字
        text_width = draw.textlength(code, font=font) if hasattr(draw, 'textlength') else len(code) * 15
        x = (width - text_width) // 2
        y = (height - 20) // 2

        # 为每个字符添加随机颜色和位置偏移
        for i, char in enumerate(code):
            char_x = x + i * (text_width // len(code))
            char_y = y + random.randint(-3, 3)
            color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
            draw.text((char_x, char_y), char, fill=color, font=font)

        # 添加干扰线
        for _ in range(3):
            x1, y1 = random.randint(0, width), random.randint(0, height)
            x2, y2 = random.randint(0, width), random.randint(0, height)
            draw.line([(x1, y1), (x2, y2)], fill=(random.randint(100, 200), random.randint(100, 200), random.randint(100, 200)), width=1)

        # 添加噪点
        for _ in range(20):
            x, y = random.randint(0, width-1), random.randint(0, height-1)
            draw.point((x, y), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))

        return image
    except Exception as e:
        logger.error(f"创建验证码图片失败: {e}", exc_info=True)
        # 返回简单的文本图片作为备选
        image = Image.new('RGB', (120, 40), color='lightgray')
        draw = ImageDraw.Draw(image)
        draw.text((10, 10), code, fill='black')
        return image

def save_captcha_to_session(session_id, code):
    """保存验证码到数据库会话"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        # 清理过期的验证码（超过5分钟）
        c.execute("DELETE FROM captcha_sessions WHERE created_time < datetime('now', '-5 minutes')")

        # 保存新的验证码
        c.execute("INSERT INTO captcha_sessions (session_id, captcha_code) VALUES (?, ?)",
                  (session_id, code.upper()))

        conn.commit()
        conn.close()
        return True
    except sqlite3.Error as e:
        logger.error(f"保存验证码失败: {e}", exc_info=True)
        return False

def verify_captcha(session_id, user_input):
    """验证验证码"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        # 查找未使用的验证码
        c.execute("""SELECT captcha_code FROM captcha_sessions
                     WHERE session_id = ? AND used = 0
                     AND created_time > datetime('now', '-5 minutes')
                     ORDER BY created_time DESC LIMIT 1""", (session_id,))

        result = c.fetchone()
        if result and result['captcha_code'].upper() == user_input.upper():
            # 标记验证码为已使用
            c.execute("UPDATE captcha_sessions SET used = 1 WHERE session_id = ? AND captcha_code = ?",
                      (session_id, result['captcha_code']))
            conn.commit()
            conn.close()
            return True

        conn.close()
        return False
    except sqlite3.Error as e:
        logger.error(f"验证验证码失败: {e}", exc_info=True)
        return False

def record_login_attempt(ip_address, username, success, user_agent=None):
    """记录登录尝试"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        c.execute("""INSERT INTO login_attempts (ip_address, username, success, user_agent)
                     VALUES (?, ?, ?, ?)""", (ip_address, username, 1 if success else 0, user_agent))

        conn.commit()
        conn.close()
        return True
    except sqlite3.Error as e:
        logger.error(f"记录登录尝试失败: {e}", exc_info=True)
        return False

def get_failed_login_count(ip_address, minutes=1):
    """获取指定时间内的失败登录次数"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        c.execute("""SELECT COUNT(*) as count FROM login_attempts
                     WHERE ip_address = ? AND success = 0
                     AND attempt_time > datetime('now', '-{} minutes')""".format(minutes), (ip_address,))

        result = c.fetchone()
        conn.close()
        return result['count'] if result else 0
    except sqlite3.Error as e:
        logger.error(f"获取失败登录次数失败: {e}", exc_info=True)
        return 0

def should_require_captcha(ip_address):
    """判断是否需要验证码"""
    failed_count = get_failed_login_count(ip_address, minutes=1)
    return failed_count >= 3

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 路由: 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    user_agent = request.headers.get('User-Agent', '')

    # 检查是否需要验证码
    require_captcha = should_require_captcha(client_ip)

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        captcha_input = request.form.get('captcha', '')

        # 如果需要验证码，先验证验证码
        if require_captcha:
            if not captcha_input:
                flash('请输入验证码', 'danger')
                record_login_attempt(client_ip, username, False, user_agent)
                return render_template('login.html', require_captcha=True)

            if not verify_captcha(session.get('session_id', ''), captcha_input):
                flash('验证码错误，请重新输入', 'danger')
                record_login_attempt(client_ip, username, False, user_agent)
                return render_template('login.html', require_captcha=True)

        # 验证用户名和密码
        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            session['user_id'] = username
            record_login_attempt(client_ip, username, True, user_agent)
            logger.info(f"用户 {username} 从 {client_ip} 登录成功")
            return redirect(url_for('index'))
        else:
            flash('用户名或密码不正确', 'danger')
            record_login_attempt(client_ip, username, False, user_agent)
            logger.warning(f"用户 {username} 从 {client_ip} 登录失败")

            # 重新检查是否需要验证码
            require_captcha = should_require_captcha(client_ip)

    return render_template('login.html', require_captcha=require_captcha)

# 路由: 登出
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

# 路由: 生成验证码
@app.route('/captcha')
def captcha():
    """生成验证码图片"""
    try:
        # 生成验证码
        code = generate_captcha_code()

        # 确保session有ID
        if 'session_id' not in session:
            session['session_id'] = ''.join(random.choices(string.ascii_letters + string.digits, k=32))

        # 保存验证码到数据库
        save_captcha_to_session(session['session_id'], code)

        # 创建验证码图片
        image = create_captcha_image(code)

        # 将图片转换为base64
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        return Response(img_buffer.getvalue(), mimetype='image/png')

    except Exception as e:
        logger.error(f"生成验证码失败: {e}", exc_info=True)
        # 返回错误图片
        error_image = Image.new('RGB', (120, 40), color='red')
        draw = ImageDraw.Draw(error_image)
        draw.text((10, 10), 'ERROR', fill='white')

        img_buffer = io.BytesIO()
        error_image.save(img_buffer, format='PNG')
        img_buffer.seek(0)

        return Response(img_buffer.getvalue(), mimetype='image/png')

# 路由: 首页/控制面板
@app.route('/')
@login_required
def index():
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取最近的信号，包括信号价格和实际成交价格
        c.execute('''
            SELECT s.id, s.timestamp, s.trading_pair, s.signal_type, s.processed,
                   s.order_ticket, s.order_result, s.close_price as signal_price,
                   o.price as order_price
            FROM signals s
            LEFT JOIN orders o ON s.id = o.signal_id
            ORDER BY s.timestamp DESC LIMIT 10
        ''')
        signals = c.fetchall()
        
        # 强制初始化MT5连接以确保获取最新数据
        mt5_connection_initialized = mt5_trader.init_mt5()
        logger.info(f"首页MT5连接初始化结果: {mt5_connection_initialized}")

        # 获取活跃订单 - 优先从MT5获取实时数据
        active_orders = []

        # 1. 首先从MT5获取所有活跃持仓（这是最准确的数据源）
        mt5_positions = []
        if mt5_connection_initialized:
            try:
                mt5_positions = mt5_trader.get_all_positions()
                logger.info(f"从MT5获取到 {len(mt5_positions)} 个活跃持仓")

                # 将MT5持仓转换为统一格式
                for position in mt5_positions:
                    # 计算持仓时长
                    open_time = datetime.fromtimestamp(position.get('time', 0))
                    duration = datetime.now() - open_time if open_time else None

                    formatted_position = {
                        'id': None,  # 数据库ID为空，表示直接来自MT5
                        'timestamp': position['time'],
                        'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S') if open_time else 'Unknown',
                        'duration': f"{duration.days}天 {duration.seconds//3600}小时" if duration else 'Unknown',
                        'ticket': position['ticket'],
                        'trading_pair': position['symbol'],
                        'operation': position['type_str'],
                        'volume': position['volume'],
                        'price': position['price_open'],
                        'current_price': position['price_current'],
                        'sl': position['sl'] if position['sl'] != 0.0 else None,
                        'tp': position['tp'] if position['tp'] != 0.0 else None,
                        'status': 'open',
                        'profit': position['profit'],
                        'swap': position.get('swap', 0.0),
                        'comment': position.get('comment', ''),
                        'magic': position.get('magic', 0),
                        'from_mt5': True  # 标记为直接从MT5获取
                    }
                    active_orders.append(formatted_position)

            except Exception as e:
                logger.error(f"获取MT5持仓失败: {e}", exc_info=True)
        else:
            logger.warning("MT5未连接，无法获取实时持仓数据")

        # 2. 从数据库获取活跃订单作为备用（仅当MT5无法连接时使用）
        if not mt5_connection_initialized or len(active_orders) == 0:
            logger.info("从数据库获取活跃订单作为备用数据")
            c.execute('''
                SELECT id, timestamp, ticket, trading_pair, operation, volume, price, sl, tp, status, profit
                FROM orders WHERE status IN ('open', 'partially_closed')
                ORDER BY timestamp DESC
            ''')
            db_active_orders = c.fetchall()

            # 如果MT5没有数据，使用数据库数据
            if len(active_orders) == 0:
                for order in db_active_orders:
                    order_dict = dict(order)
                    order_dict['from_mt5'] = False  # 标记为来自数据库
                    order_dict['current_price'] = order_dict.get('price', 0)  # 使用开仓价作为当前价
                    active_orders.append(order_dict)

        logger.info(f"最终获取到 {len(active_orders)} 个活跃订单")
        
        # 获取最近1天的已平仓订单
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()

        c.execute('''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, close_price,
                   profit, status, close_time
            FROM orders
            WHERE status = 'closed' AND close_time >= ?
            ORDER BY close_time DESC LIMIT 20
        ''', (yesterday,))
        closed_orders = c.fetchall()

        # 如果数据库中的已平仓订单较少，尝试从MT5获取今日已平仓交易
        if len(closed_orders) < 10 and mt5_connection_initialized:
            try:
                today_start = datetime.combine(datetime.today(), datetime.min.time())
                today_end = datetime.combine(datetime.today(), datetime.max.time())

                # 获取今日已平仓订单
                today_deals = mt5_trader.get_history_deals(from_date=today_start, to_date=today_end, mt5_initialized=mt5_connection_initialized, init_mt5=mt5_trader.init_mt5)

                # 筛选买卖类型的交易记录并且是出场(DEAL_ENTRY_OUT)的记录
                mt5_closed_deals = []
                for deal in today_deals:
                    if (deal['type'] in [mt5.DEAL_TYPE_BUY, mt5.DEAL_TYPE_SELL] and
                        deal['entry'] == mt5.DEAL_ENTRY_OUT and
                        deal['profit'] is not None):

                        # 转换为与数据库订单相同的格式
                        mt5_closed_deals.append({
                            'id': None,  # MT5数据没有数据库ID
                            'timestamp': deal['time'].isoformat() if hasattr(deal['time'], 'isoformat') else str(deal['time']),
                            'ticket': deal['position_id'],
                            'trading_pair': deal['symbol'],
                            'operation': 'buy' if deal['type'] == mt5.DEAL_TYPE_BUY else 'sell',
                            'volume': deal['volume'],
                            'price': deal.get('price_open', 0),  # 开仓价
                            'close_price': deal['price'],  # 平仓价
                            'profit': deal['profit'],
                            'status': 'closed',
                            'close_time': deal['time'].isoformat() if hasattr(deal['time'], 'isoformat') else str(deal['time']),
                            'from_mt5': True  # 标记为来自MT5
                        })

                # 合并数据库和MT5的已平仓订单，去重
                existing_tickets = {order['ticket'] for order in closed_orders}
                for mt5_deal in mt5_closed_deals:
                    if mt5_deal['ticket'] not in existing_tickets:
                        closed_orders.append(mt5_deal)

                # 按平仓时间排序并限制数量 - 处理混合的时间戳格式
                def get_close_time_for_sort(order):
                    """获取用于排序的平仓时间，统一转换为datetime对象"""
                    try:
                        close_time = order['close_time']
                        if isinstance(close_time, str):
                            # 处理ISO格式字符串
                            if close_time.endswith('Z'):
                                close_time = close_time.replace('Z', '+00:00')
                            return datetime.fromisoformat(close_time)
                        elif isinstance(close_time, (int, float)):
                            # 处理Unix时间戳
                            return datetime.fromtimestamp(close_time)
                        else:
                            # 如果是其他类型，返回当前时间作为默认值
                            logger.warning(f"未知的平仓时间格式: {type(close_time)} - {close_time}")
                            return datetime.now()
                    except Exception as e:
                        logger.error(f"解析平仓时间失败: {e}, close_time: {order.get('close_time')}")
                        return datetime.now()  # 返回当前时间作为默认值

                closed_orders = sorted(closed_orders, key=get_close_time_for_sort, reverse=True)[:20]

                logger.info(f"合并后获取到 {len(closed_orders)} 个已平仓订单")

            except Exception as e:
                logger.error(f"从MT5获取已平仓订单失败: {e}", exc_info=True)
        
        # 获取交易统计
        c.execute('''
            SELECT COUNT(*) as total_signals,
                   SUM(CASE WHEN processed = 1 THEN 1 ELSE 0 END) as processed_signals,
                   SUM(CASE WHEN order_ticket IS NOT NULL THEN 1 ELSE 0 END) as executed_signals
            FROM signals
        ''')
        signal_stats = c.fetchone()
        
        c.execute('''
            SELECT COUNT(*) as total_orders,
                   SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_orders,
                   SUM(CASE WHEN status IN ('open', 'partially_closed') THEN 1 ELSE 0 END) as active_orders,
                   SUM(CASE WHEN profit > 0 AND status = 'closed' THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 AND status = 'closed' THEN 1 ELSE 0 END) as loss_orders,
                   SUM(CASE WHEN profit IS NOT NULL THEN profit ELSE 0 END) as total_profit
            FROM orders
        ''')
        order_stats = c.fetchone()
        
        # 处理None值
        if order_stats:
            order_stats = dict(order_stats)
            for key in order_stats:
                if order_stats[key] is None:
                    order_stats[key] = 0
        
        conn.close()
        
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取交易开关状态
        trading_enabled = config.get('enable_trading', False)
        
        # 检查MT5连接状态（使用之前初始化的连接）
        try:
            terminal_info = mt5.terminal_info()
            mt5_connected = terminal_info is not None and mt5_connection_initialized
            if mt5_connected:
                logger.info(f"MT5已连接，终端名称: {terminal_info.name}")
            else:
                logger.warning("MT5未连接，terminal_info返回None或初始化失败")
        except Exception as e:
            logger.error(f"检查MT5连接状态时出错: {e}")
            mt5_connected = False
            
        logger.info(f"MT5连接状态: {'已连接' if mt5_connected else '未连接'}")
        
        # 检查信号接收服务状态
        signal_receiver_running = check_signal_receiver_status()
        logger.info(f"信号接收服务状态: {'运行中' if signal_receiver_running else '未运行'}")
        
        # 获取账户信息
        account_info = mt5_trader.get_account_info() if mt5_connected else {
            'balance': 0.0,
            'equity': 0.0,
            'profit': 0.0,
            'margin': 0.0,
            'free_margin': 0.0,
            'margin_level': 0.0,
            'currency': 'USD'
        }
        logger.info(f"获取账户信息: {account_info}")
        
        return render_template('index.html',
                              signals=signals,
                              active_orders=active_orders,
                              closed_orders=closed_orders,
                              signal_stats=signal_stats,
                              order_stats=order_stats,
                              trading_enabled=trading_enabled,
                              mt5_connected=mt5_connected,
                              signal_receiver_running=signal_receiver_running,
                              account_info=account_info)
    
    except Exception as e:
        logger.error(f"加载首页数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        # Provide defaults so template has required variables
        return render_template('index.html',
                              signals=[],
                              active_orders=[],
                              closed_orders=[],
                              signal_stats={'total_signals': 0, 'processed_signals': 0, 'executed_signals': 0},
                              order_stats={'total_orders': 0, 'closed_orders': 0, 'active_orders': 0, 'profitable_orders': 0, 'loss_orders': 0, 'total_profit': 0},
                              trading_enabled=False,
                              mt5_connected=False,
                              signal_receiver_running=False,
                              account_info={
                                  'balance': 0.0,
                                  'equity': 0.0, 
                                  'profit': 0.0,
                                  'margin': 0.0,
                                  'free_margin': 0.0,
                                  'margin_level': 0.0,
                                  'currency': 'USD'
                              })

# 路由: 信号管理
@app.route('/signals')
@login_required
def signals():
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        offset = (page - 1) * per_page
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取总条数
        c.execute('SELECT COUNT(*) FROM signals')
        total = c.fetchone()[0]
        
        # 获取分页数据 - 添加更多字段用于信号管理页面显示
        c.execute('''
            SELECT
                s.id,
                s.timestamp,
                s.trading_pair,
                s.signal_type,
                s.interval,
                s.mrc_event,
                s.close_price AS price,
                s.s1 AS sl,
                s.r1 AS tp,
                s.mean,
                s.r2,
                s.s2,
                s.r2_1,
                s.s2_1,
                s.r2_9,
                s.s2_9,
                s.processed,
                s.process_time,
                s.order_ticket,
                s.order_result,
                o.price AS order_price
            FROM signals s
            LEFT JOIN orders o ON s.id = o.signal_id
            ORDER BY s.timestamp DESC
            LIMIT ? OFFSET ?
        ''', (per_page, offset))
        signals = c.fetchall()
        
        conn.close()
        
        # 计算总页数
        total_pages = (total + per_page - 1) // per_page
        
        return render_template('signals.html',
                              signals=signals,
                              page=page,
                              per_page=per_page,
                              total=total,
                              total_pages=total_pages)
    
    except Exception as e:
        logger.error(f"加载信号数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('signals.html', signals=[])

# 路由: 信号详情
@app.route('/signal/<int:signal_id>')
@login_required
def signal_detail(signal_id):
    try:
        conn = get_db_connection()
        c = conn.cursor()

        # 获取信号数据，包括新增的字段
        c.execute('''
            SELECT s.*,
                   s.close_price AS price,
                   s.s1 AS sl,
                   s.r1 AS tp
            FROM signals s
            WHERE s.id = ?
        ''', (signal_id,))
        signal = c.fetchone()

        if signal:
            # 将结果转换为字典，以便更容易修改
            signal = dict(signal)

            # 获取关联订单及其真实盈亏
            c.execute('''
                SELECT o.*,
                       CASE
                           WHEN o.status = 'open' THEN
                               (SELECT profit FROM orders WHERE ticket = o.ticket LIMIT 1)
                           ELSE o.profit
                       END as real_profit
                FROM orders o
                WHERE o.signal_id = ?
            ''', (signal_id,))
            order = c.fetchone()

            if order:
                order = dict(order)
                # 如果订单还在持仓中，尝试从MT5获取最新盈亏
                if order['status'] == 'open' and order.get('ticket'):
                    try:
                        mt5_positions = mt5_trader.get_all_positions()
                        mt5_order = next((pos for pos in mt5_positions if pos['ticket'] == order['ticket']), None)
                        if mt5_order:
                            order['real_profit'] = mt5_order['profit']
                            order['current_price'] = mt5_order['price_current']
                    except Exception as e:
                        logger.warning(f"获取MT5实时盈亏失败: {e}")

                # 确保所有需要的字段都存在
                if 'profit' not in order or order['profit'] is None:
                    order['profit'] = 0.0
                if 'real_profit' not in order or order['real_profit'] is None:
                    order['real_profit'] = order.get('profit', 0.0)
        else:
            signal = None
            order = None

        conn.close()

        if not signal:
            flash('信号不存在', 'danger')
            return redirect(url_for('signals'))

        # 确保所有可能在模板中使用的字段都有默认值
        default_fields = {
            'mrc_event': '-',
            'interval': '-',
            'red_index': '-',
            'blue_index': '-',
            'green_index': '-',
            'additional_data': '-',
            'process_time': None,
            'webhook_content': None,
            'process_status': 'pending',
            'failure_reason': None,
            'success_details': None
        }

        for key, default_value in default_fields.items():
            if key not in signal or signal[key] is None:
                signal[key] = default_value

        return render_template('signal_detail.html', signal=signal, order=order)

    except Exception as e:
        logger.error(f"加载信号详情失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return redirect(url_for('signals'))

# 路由: 订单管理
@app.route('/orders')
@login_required
def orders():
    try:
        # 获取分页和筛选参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', 'all')
        
        # 强制初始化MT5连接确保获取最新数据
        mt5_connected = mt5_trader.init_mt5()
        logger.info(f"访问订单页面，MT5连接状态: {mt5_connected}")
        
        # 创建订单列表
        orders = []
        
        try:
            if not mt5_connected:
                flash('MetaTrader5连接失败，无法获取实时订单数据', 'warning')
            else:
                # 1. 获取MT5真实持仓（活跃订单）
                mt5_positions = mt5_trader.get_all_positions()
                for position in mt5_positions:
                    # 计算持仓时长
                    open_time = datetime.fromtimestamp(position.get('time', 0))
                    duration = datetime.now() - open_time if open_time else None
                    
                    # 查找关联的信号数据以获取S2、R2值
                    signal_s2 = None
                    signal_r2 = None
                    try:
                        conn = get_db_connection()
                        c = conn.cursor()
                        # 根据订单号查找关联信号
                        c.execute('SELECT s2, r2 FROM signals WHERE order_ticket = ?', (position.get('ticket'),))
                        signal_data = c.fetchone()
                        if signal_data:
                            signal_s2 = signal_data['s2']
                            signal_r2 = signal_data['r2']
                        conn.close()
                    except Exception as e:
                        logger.warning(f"查找订单 {position.get('ticket')} 关联信号失败: {e}")

                    # 添加更丰富的订单信息
                    orders.append({
                        'id': None,  # 数据库ID为空
                        'timestamp': position.get('time'),
                        'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S') if open_time else 'Unknown',
                        'duration': f"{duration.days}天 {duration.seconds//3600}小时" if duration else 'Unknown',
                        'ticket': position.get('ticket'),
                        'trading_pair': position.get('symbol'),
                        'operation': position.get('type_str'),
                        'volume': position.get('volume'),
                        'price': position.get('price_open'),
                        'current_price': position.get('price_current'),
                        'sl': position.get('sl'),
                        'tp': position.get('tp'),
                        'status': 'open',
                        'profit': position.get('profit'),
                        'swap': position.get('swap'),
                        'comment': position.get('comment'),
                        'magic': position.get('magic'),
                        'close_price': None,
                        'close_time': None,
                        'signal_id': None,
                        'signal_s2': signal_s2,
                        'signal_r2': signal_r2,
                        'from_mt5': True
                    })
                
                # 2. 获取今天的MT5已平仓订单
                if status == 'all' or status == 'closed':
                    # 获取今天的日期范围
                    today_start = datetime.combine(datetime.today(), datetime.min.time())
                    today_end = datetime.combine(datetime.today(), datetime.max.time())

                    try:
                        # 获取今日已平仓订单 - 扩大时间范围到最近7天
                        week_start = datetime.combine(datetime.today() - timedelta(days=7), datetime.min.time())

                        # 获取最近7天的已平仓订单
                        recent_deals = mt5_trader.get_history_deals(from_date=week_start, to_date=today_end, mt5_initialized=mt5_connected, init_mt5=mt5_trader.init_mt5)

                        logger.info(f"从MT5获取到 {len(recent_deals)} 条历史交易记录")

                        # 筛选买卖类型的交易记录并且是出场(DEAL_ENTRY_OUT)的记录
                        closed_deals = []
                        for deal in recent_deals:
                            logger.debug(f"处理交易记录: type={deal.get('type')}, entry={deal.get('entry')}, profit={deal.get('profit')}")
                            # 修正：DEAL_ENTRY_OUT的值是1
                            if (deal['type'] in [0, 1] and  # 0=买入, 1=卖出
                                deal['entry'] == 1 and      # 1=出场(DEAL_ENTRY_OUT)
                                deal['profit'] is not None):
                                closed_deals.append(deal)
                                logger.debug(f"添加已平仓交易: {deal['position_id']}, profit={deal['profit']}")

                        logger.info(f"筛选后获得 {len(closed_deals)} 条已平仓交易记录")
                        
                        # 将平仓交易记录转换为订单格式
                        for deal in closed_deals:
                            close_time = datetime.fromisoformat(deal['time'].replace('Z', '+00:00')) if deal['time'].endswith('Z') else datetime.fromisoformat(deal['time'])

                            # 从数据库中获取订单的止损止盈数据和信号数据
                            order_sl = 0
                            order_tp = 0
                            signal_s2 = None
                            signal_r2 = None
                            signal_id = None

                            try:
                                conn = get_db_connection()
                                c = conn.cursor()

                                # 首先查找数据库中对应的订单记录，获取止损止盈数据
                                c.execute('SELECT sl, tp, signal_id FROM orders WHERE ticket = ?', (deal['position_id'],))
                                order_data = c.fetchone()

                                if order_data:
                                    order_sl = order_data['sl'] or 0
                                    order_tp = order_data['tp'] or 0
                                    signal_id = order_data['signal_id']

                                    # 如果有关联的信号ID，获取S2、R2数据
                                    if signal_id:
                                        c.execute('SELECT s2, r2 FROM signals WHERE id = ?', (signal_id,))
                                        signal_data = c.fetchone()
                                        if signal_data:
                                            signal_s2 = signal_data['s2']
                                            signal_r2 = signal_data['r2']
                                else:
                                    # 如果数据库中没有订单记录，尝试通过订单票号查找信号
                                    c.execute('SELECT s2, r2, id FROM signals WHERE order_ticket = ?', (deal['position_id'],))
                                    signal_data = c.fetchone()
                                    if signal_data:
                                        signal_s2 = signal_data['s2']
                                        signal_r2 = signal_data['r2']
                                        signal_id = signal_data['id']

                                conn.close()
                            except Exception as e:
                                logger.warning(f"查找已平仓订单 {deal['position_id']} 数据失败: {e}")

                            # 计算持仓时长
                            try:
                                duration_str = '已平仓'
                                # 尝试获取开仓时间来计算持仓时长
                                # 注意：MT5的deal记录中可能没有开仓时间，这里只是示例
                            except:
                                duration_str = '已平仓'

                            # 添加已平仓订单
                            orders.append({
                                'id': None,  # 数据库ID为空
                                'ticket': deal['position_id'],  # 使用position_id作为订单号
                                'timestamp': deal['time'],  # ISO格式时间
                                'time_formatted': close_time.strftime('%Y-%m-%d %H:%M:%S'),
                                'duration': duration_str,
                                'trading_pair': deal['symbol'],
                                'operation': 'buy' if deal['type'] == 0 else 'sell',  # 0=DEAL_TYPE_BUY, 1=DEAL_TYPE_SELL
                                'volume': deal['volume'],
                                'price': deal['price'],
                                'current_price': deal['price'],  # 平仓价即当前价
                                'sl': order_sl,  # 从数据库订单记录获取
                                'tp': order_tp,  # 从数据库订单记录获取
                                'status': 'closed',
                                'profit': deal['profit'],
                                'swap': deal.get('swap', 0),
                                'comment': deal.get('comment', ''),
                                'magic': deal.get('magic', 0),
                                'close_price': deal['price'],
                                'close_time': deal['time'],
                                'signal_id': signal_id,
                                'signal_s2': signal_s2,
                                'signal_r2': signal_r2,
                                'from_mt5': True,
                                'reason': deal.get('reason_str', '手动平仓'),
                                'reason_str': deal.get('reason_str', '手动平仓')
                            })
                    except Exception as e:
                        logger.error(f"获取MT5今日已平仓交易记录失败: {e}", exc_info=True)
                        flash(f'获取今日已平仓订单失败: {str(e)}', 'warning')
            
            logger.info(f"订单总数: {len(orders)}")
            
            # 根据状态过滤
            if status != 'all':
                orders = [order for order in orders if order['status'] == status]
            
            # 按时间倒序排序 - 处理混合的时间戳格式
            def get_sort_timestamp(order):
                """获取用于排序的时间戳，统一转换为datetime对象"""
                try:
                    timestamp = order['timestamp']
                    if isinstance(timestamp, str):
                        # 处理ISO格式字符串
                        if timestamp.endswith('Z'):
                            # 移除Z后缀并添加UTC时区信息
                            timestamp = timestamp.replace('Z', '+00:00')
                        return datetime.fromisoformat(timestamp)
                    elif isinstance(timestamp, (int, float)):
                        # 处理Unix时间戳
                        return datetime.fromtimestamp(timestamp)
                    else:
                        # 如果是其他类型，返回当前时间作为默认值
                        logger.warning(f"未知的时间戳格式: {type(timestamp)} - {timestamp}")
                        return datetime.now()
                except Exception as e:
                    logger.error(f"解析时间戳失败: {e}, timestamp: {order.get('timestamp')}")
                    return datetime.now()  # 返回当前时间作为默认值

            orders.sort(key=get_sort_timestamp, reverse=True)
            
            total = len(orders)
            total_pages = (total + per_page - 1) // per_page if per_page > 0 else 1
            
            # 分页
            start = (page - 1) * per_page
            end = start + per_page
            orders = orders[start:end]
            
        except Exception as e:
            logger.error(f"获取MT5订单数据失败: {e}", exc_info=True)
            flash(f'获取MT5订单数据失败: {str(e)}', 'danger')
            total = 0
            total_pages = 1

        return render_template('orders.html',
                              orders=orders,
                              page=page,
                              per_page=per_page,
                              total=total,
                              total_pages=total_pages,
                              status=status)
    
    except Exception as e:
        logger.error(f"加载订单数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('orders.html', 
                              orders=[], 
                              page=1, 
                              per_page=20, 
                              total=0, 
                              total_pages=1, 
                              status='all')

# 路由: 订单详情
@app.route('/order/<int:order_id>')
@login_required
def order_detail(order_id):
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        c.execute('SELECT * FROM orders WHERE id = ?', (order_id,))
        order = c.fetchone()
        signal = None
        
        if order:
            # 获取关联信号
            c.execute('SELECT * FROM signals WHERE id = ?', (order['signal_id'],))
            signal = c.fetchone()
            
            # 尝试从MT5获取最新状态（如果订单还在MT5中）
            try:
                ticket = order['ticket']
                mt5_positions = mt5_trader.get_all_positions()
                mt5_order = next((pos for pos in mt5_positions if pos['ticket'] == ticket), None)
                
                if mt5_order:
                    # 更新订单信息为MT5最新数据
                    order_dict = dict(order)  # 转换为字典以便修改
                    order_dict['profit'] = mt5_order['profit']
                    order_dict['sl'] = mt5_order['sl']
                    order_dict['tp'] = mt5_order['tp']
                    order_dict['price_current'] = mt5_order['price_current']
                    order_dict['swap'] = mt5_order['swap']
                    order_dict['mt5_time'] = datetime.fromtimestamp(mt5_order['time']).strftime('%Y-%m-%d %H:%M:%S')
                    duration = datetime.now() - datetime.fromtimestamp(mt5_order['time'])
                    order_dict['duration'] = f"{duration.days}天 {duration.seconds//3600}小时"
                    order_dict['from_mt5'] = True
                    order = order_dict
                    
                    logger.info(f"从MT5获取到订单 {ticket} 的最新数据")
            except Exception as e:
                logger.error(f"获取MT5订单状态失败: {e}", exc_info=True)
        
        conn.close()
        
        if not order:
            flash('订单不存在', 'danger')
            return redirect(url_for('orders'))
        
        return render_template('order_detail.html', order=order, signal=signal)
    
    except Exception as e:
        logger.error(f"加载订单详情失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return redirect(url_for('orders'))

# 路由: 平仓操作
@app.route('/close_position', methods=['POST'])
@login_required
def close_position():
    try:
        ticket = request.form.get('ticket')
        if not ticket:
            return jsonify({'success': False, 'message': '订单号不能为空'})
        
        ticket = int(ticket)
        
        # 检查是否部分平仓
        volume = request.form.get('volume')
        if volume:
            volume = float(volume)
        else:
            volume = None
        
        # 执行平仓
        logger.info(f"Web界面平仓请求: 订单{ticket}, 交易量{volume}")
        result = mt5_trader.close_position(ticket, volume)

        if result and result.get('success', False):
            logger.info(f"Web界面平仓成功: 订单{ticket}, 结果{result}")
            return jsonify({'success': True, 'message': '平仓成功'})
        else:
            logger.error(f"Web界面平仓失败: 订单{ticket}, 结果{result}")
            return jsonify({'success': False, 'message': '平仓失败，请查看日志'})
    
    except Exception as e:
        logger.error(f"平仓操作失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'平仓操作异常: {str(e)}'})

# 路由: 修改止损止盈
@app.route('/modify_sl_tp', methods=['POST'])
@login_required
def modify_sl_tp():
    try:
        ticket = request.form.get('ticket')
        sl = request.form.get('sl')
        tp = request.form.get('tp')
        
        if not ticket:
            return jsonify({'success': False, 'message': '订单号不能为空'})
        
        ticket = int(ticket)
        sl = float(sl) if sl else None
        tp = float(tp) if tp else None
        
        # 执行修改
        result = mt5_trader.modify_sl_tp(ticket, sl, tp)
        
        if result:
            return jsonify({'success': True, 'message': '修改成功'})
        else:
            return jsonify({'success': False, 'message': '修改失败，请查看日志'})
    
    except Exception as e:
        logger.error(f"修改止损止盈失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'修改操作异常: {str(e)}'})
        
# 路由: 手动执行信号
@app.route('/manual_process_signal', methods=['POST'])
@login_required
def manual_process_signal():
    try:
        signal_id = request.form.get('signal_id')
        
        if not signal_id:
            return jsonify({'success': False, 'message': '信号ID不能为空'})
        
        signal_id = int(signal_id)
        
        # 获取信号信息
        conn = get_db_connection()
        c = conn.cursor()
        c.execute('SELECT * FROM signals WHERE id = ?', (signal_id,))
        signal = c.fetchone()
        
        if not signal:
            conn.close()
            return jsonify({'success': False, 'message': '信号不存在'})
        
        if signal['processed'] == 1:
            conn.close()
            return jsonify({'success': False, 'message': '该信号已经处理过'})
        
        # 从配置文件读取交易启用状态
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if not config.get('enable_trading', False):
            conn.close()
            return jsonify({'success': False, 'message': '交易功能已关闭，请先在设置中开启'})
        
        # 调用MT5交易模块执行信号
        # 直接使用信号数据字典调用execute_trade函数
        signal_dict = dict(signal)
        logger.info(f"尝试执行信号 ID: {signal_id}, 交易对: {signal_dict.get('trading_pair')}, 类型: {signal_dict.get('signal_type')}")
        
        # 执行交易
        result = mt5_trader.execute_trade(signal_dict)
        logger.info(f"信号 ID: {signal_id} 执行结果: {result}")
        
        # 更新信号处理状态
        if result:
            c.execute('''
                UPDATE signals 
                SET processed = 1, process_time = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), signal_id))
            conn.commit()
            conn.close()
            return jsonify({'success': True, 'message': '信号执行成功'})
        else:
            c.execute('''
                UPDATE signals 
                SET processed = 1, process_time = ?, order_result = ?
                WHERE id = ?
            ''', (datetime.now().isoformat(), '交易执行失败', signal_id))
            conn.commit()
            conn.close()
            return jsonify({'success': False, 'message': '交易执行失败，请查看日志了解详情'})
    
    except Exception as e:
        logger.error(f"手动执行信号失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'执行异常: {str(e)}'})

# 路由: 添加手动信号
@app.route('/add_signal', methods=['POST'])
@login_required
def add_signal():
    try:
        trading_pair = request.form.get('trading_pair')
        signal_type = request.form.get('signal_type')
        price = float(request.form.get('price', 0))
        sl = float(request.form.get('sl')) if request.form.get('sl') else None
        tp = float(request.form.get('tp')) if request.form.get('tp') else None
        volume = float(request.form.get('volume')) if request.form.get('volume') else None
        # 可根据需求添加其他字段
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取信号表结构以便检查可用字段
        c.execute("PRAGMA table_info(signals)")
        columns = [column[1] for column in c.fetchall()]
        
        # 根据表结构构建插入查询
        fields = ['timestamp', 'trading_pair', 'signal_type', 'processed']
        values = [datetime.now().isoformat(), trading_pair, signal_type, 0]
        
        # 添加可能的额外字段
        if 'price' in columns and price is not None:
            fields.append('close_price')  # 假设交易价格存储在close_price字段中
            values.append(price)
        
        if 'sl' in columns and sl is not None:
            fields.append('s1')  # 假设止损价格存储在s1字段中
            values.append(sl)
        
        if 'tp' in columns and tp is not None:
            fields.append('r1')  # 假设止盈价格存储在r1字段中
            values.append(tp)
        
        if 'volume' in columns and volume is not None:
            fields.append('volume')
            values.append(volume)
            
        # 添加空值字段
        fields.extend(['order_ticket', 'order_result', 'interval', 'mrc_event'])
        values.extend([None, None, None, None])
        
        # 构建并执行SQL
        placeholders = ', '.join(['?' for _ in values])
        query = f"INSERT INTO signals ({', '.join(fields)}) VALUES ({placeholders})"
        
        logger.info(f"添加手动信号，SQL: {query}")
        logger.info(f"添加手动信号，参数: {values}")
        
        c.execute(query, values)
        signal_id = c.lastrowid
        conn.commit()
        conn.close()
        
        logger.info(f"成功添加手动信号，ID: {signal_id}")
        flash('手动信号已添加', 'success')
    except Exception as e:
        logger.error(f"添加手动信号失败: {e}", exc_info=True)
        flash('添加手动信号失败', 'danger')
    return redirect(url_for('signals'))

# 路由: 系统设置
@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if request.method == 'POST':
        try:
            # 读取当前配置
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 更新交易开关
            enable_trading = request.form.get('enable_trading') == 'on'
            config['enable_trading'] = enable_trading
            
            # 更新交易时段过滤功能
            enable_time_filter = request.form.get('enable_time_filter') == 'on'
            config['enable_time_filter'] = enable_time_filter

            # 更新MT5连接配置
            mt5_login = request.form.get('mt5_login')
            if mt5_login and mt5_login.strip():
                # 验证登录ID是否为数字
                try:
                    int(mt5_login.strip())
                    config['mt5_login'] = mt5_login.strip()
                except ValueError:
                    flash('MT5登录ID必须是纯数字', 'danger')
                    return redirect(url_for('settings'))

            mt5_password = request.form.get('mt5_password')
            if mt5_password and mt5_password.strip():
                config['mt5_password'] = mt5_password.strip()

            mt5_server = request.form.get('mt5_server')
            if mt5_server and mt5_server.strip():
                config['mt5_server'] = mt5_server.strip()

            mt5_path = request.form.get('mt5_path')
            if mt5_path and mt5_path.strip():
                config['mt5_path'] = mt5_path.strip()

            # 更新交易量
            default_volume = request.form.get('default_volume')
            if default_volume:
                config['default_volume'] = float(default_volume)
            
            # 更新止损止盈点数
            default_sl_points = request.form.get('default_sl_points')
            if default_sl_points:
                config['default_sl_points'] = int(default_sl_points)
                
            default_tp_points = request.form.get('default_tp_points')
            if default_tp_points:
                config['default_tp_points'] = int(default_tp_points)
                
            # 更新止损止盈计算方式
            sl_tp_calculation_method = request.form.get('sl_tp_calculation_method')
            if sl_tp_calculation_method:
                config['sl_tp_calculation_method'] = sl_tp_calculation_method
            
            # 更新交易品种启用状态
            if 'enabled_symbols' not in config:
                config['enabled_symbols'] = {}
                
            for symbol in ['XAUUSD', 'BTCUSD', 'ETHUSD', 'GBPUSD', 'GBPJPY', 'BRENT', 'XTIUSD']:
                # 检查交易品种是否启用
                symbol_enabled = request.form.get(f'enable_symbol_{symbol}') == 'on'
                config['enabled_symbols'][symbol] = symbol_enabled
            
            # 更新交易对特定配置
            for symbol in ['BTCUSD', 'ETHUSD', 'XAUUSD', 'GBPJPY', 'GBPUSD', 'BRENT', 'XTIUSD']:
                symbol_volume = request.form.get(f'{symbol}_volume')
                if symbol_volume:
                    # 确保symbol_volumes字典存在
                    if 'symbol_volumes' not in config:
                        config['symbol_volumes'] = {}
                    config['symbol_volumes'][symbol] = float(symbol_volume)

                symbol_sl = request.form.get(f'{symbol}_sl')
                if symbol_sl:
                    # 确保symbol_sl_points字典存在
                    if 'symbol_sl_points' not in config:
                        config['symbol_sl_points'] = {}
                    config['symbol_sl_points'][symbol] = int(symbol_sl)

                symbol_tp = request.form.get(f'{symbol}_tp')
                if symbol_tp:
                    # 确保symbol_tp_points字典存在
                    if 'symbol_tp_points' not in config:
                        config['symbol_tp_points'] = {}
                    config['symbol_tp_points'][symbol] = int(symbol_tp)

                symbol_max_orders = request.form.get(f'{symbol}_max_orders')
                if symbol_max_orders:
                    # 确保symbol_max_active_orders字典存在
                    if 'symbol_max_active_orders' not in config:
                        config['symbol_max_active_orders'] = {}
                    config['symbol_max_active_orders'][symbol] = int(symbol_max_orders)
            
            # 更新交易时段配置
            if enable_time_filter:
                # 初始化交易时段配置
                if 'trading_times' not in config:
                    config['trading_times'] = {}
                
                # 处理每天的时段配置
                for day_num in ['0', '1', '2', '3', '4', '5', '6']:  # 0=周日，1=周一, ..., 6=周六
                    day_enabled = request.form.get(f'day_enabled_{day_num}') == 'on'
                    
                    # 如果该天没有配置，初始化
                    if day_num not in config['trading_times']:
                        config['trading_times'][day_num] = {
                            'enabled': False,
                            'period1': {'start': '00:00', 'end': '00:00'},
                            'period2': {'start': '00:00', 'end': '00:00'},
                        }
                    
                    # 更新启用状态
                    config['trading_times'][day_num]['enabled'] = day_enabled
                    
                    # 更新时间段1
                    period1_start = request.form.get(f'day_{day_num}_period1_start')
                    period1_end = request.form.get(f'day_{day_num}_period1_end')
                    if period1_start and period1_end:
                        config['trading_times'][day_num]['period1'] = {
                            'start': period1_start,
                            'end': period1_end
                        }
                    
                    # 更新时间段2
                    period2_start = request.form.get(f'day_{day_num}_period2_start')
                    period2_end = request.form.get(f'day_{day_num}_period2_end')
                    if period2_start and period2_end:
                        config['trading_times'][day_num]['period2'] = {
                            'start': period2_start,
                            'end': period2_end
                        }
            
            # 更新Bark通知配置
            bark_device_key = request.form.get('bark_device_key')
            if bark_device_key is not None:  # 允许空字符串
                config['bark_device_key'] = bark_device_key

            bark_device_key_2 = request.form.get('bark_device_key_2')
            if bark_device_key_2 is not None:  # 允许空字符串
                config['bark_device_key_2'] = bark_device_key_2

            bark_sound = request.form.get('bark_sound')
            if bark_sound:
                config['bark_sound'] = bark_sound

            # 新增 Bark 更多参数
            bark_url = request.form.get('bark_url')
            if bark_url:
                config['bark_url'] = bark_url
            bark_title = request.form.get('bark_title')
            if bark_title:
                config['bark_title'] = bark_title
            bark_level = request.form.get('bark_level')
            if bark_level:
                config['bark_level'] = bark_level
            bark_volume = request.form.get('bark_volume')
            if bark_volume:
                config['bark_volume'] = float(bark_volume)
            bark_badge = request.form.get('bark_badge')
            if bark_badge:
                config['bark_badge'] = int(bark_badge)
            call = request.form.get('call')
            if call is not None:
                config['call'] = call

            # 更新Bark通知类型设置
            if 'bark_notifications' not in config:
                config['bark_notifications'] = {}

            config['bark_notifications']['signal_received'] = request.form.get('notify_signal_received') == 'on'
            config['bark_notifications']['signal_processing'] = request.form.get('notify_signal_processing') == 'on'
            config['bark_notifications']['trade_execution'] = request.form.get('notify_trade_execution') == 'on'
            config['bark_notifications']['trade_closed'] = request.form.get('notify_trade_closed') == 'on'
            config['bark_notifications']['error'] = request.form.get('notify_error') == 'on'

            # 更新余额监测配置
            if 'balance_monitoring' not in config:
                config['balance_monitoring'] = {}

            # 总开关
            config['balance_monitoring']['enabled'] = request.form.get('balance_monitoring_enabled') == 'on'

            # 余额变动通知配置
            if 'balance_change_notification' not in config['balance_monitoring']:
                config['balance_monitoring']['balance_change_notification'] = {}

            config['balance_monitoring']['balance_change_notification']['enabled'] = request.form.get('balance_change_enabled') == 'on'
            min_change_amount = request.form.get('min_change_amount')
            if min_change_amount:
                config['balance_monitoring']['balance_change_notification']['min_change_amount'] = float(min_change_amount)

            # 盈亏通知配置
            if 'profit_loss_notification' not in config['balance_monitoring']:
                config['balance_monitoring']['profit_loss_notification'] = {}

            config['balance_monitoring']['profit_loss_notification']['enabled'] = request.form.get('profit_loss_enabled') == 'on'
            profit_threshold = request.form.get('profit_threshold')
            if profit_threshold:
                config['balance_monitoring']['profit_loss_notification']['profit_threshold'] = float(profit_threshold)
            loss_threshold = request.form.get('loss_threshold')
            if loss_threshold:
                config['balance_monitoring']['profit_loss_notification']['loss_threshold'] = float(loss_threshold)

            # 定时余额推送配置
            if 'periodic_balance_notification' not in config['balance_monitoring']:
                config['balance_monitoring']['periodic_balance_notification'] = {}

            config['balance_monitoring']['periodic_balance_notification']['enabled'] = request.form.get('periodic_balance_enabled') == 'on'
            interval_hours = request.form.get('interval_hours')
            if interval_hours:
                config['balance_monitoring']['periodic_balance_notification']['interval_hours'] = int(interval_hours)
            start_time = request.form.get('start_time')
            if start_time:
                config['balance_monitoring']['periodic_balance_notification']['start_time'] = start_time
            end_time = request.form.get('end_time')
            if end_time:
                config['balance_monitoring']['periodic_balance_notification']['end_time'] = end_time

            # 保存配置
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            
            flash('设置已保存', 'success')

            # 检查是否修改了MT5连接配置
            mt5_config_modified = any([
                request.form.get('mt5_login'),
                request.form.get('mt5_password'),
                request.form.get('mt5_server'),
                request.form.get('mt5_path')
            ])

            # 检查是否修改了止盈止损点数设置
            sl_tp_modified = False
            for symbol in ['BTCUSD', 'ETHUSD', 'XAUUSD', 'GBPJPY', 'GBPUSD', 'BRENT', 'XTIUSD', 'DXY']:
                symbol_sl = request.form.get(f'{symbol}_sl')
                symbol_tp = request.form.get(f'{symbol}_tp')
                if symbol_sl or symbol_tp:
                    sl_tp_modified = True
                    break

            if mt5_config_modified:
                # MT5连接配置被修改，需要重启连接
                def restart_mt5_connection():
                    try:
                        logger.info("MT5连接配置已修改，正在重启连接...")
                        # 关闭现有连接
                        mt5.shutdown()
                        # 重新初始化MT5连接
                        if mt5_trader.init_mt5():
                            logger.info("MT5连接重启成功")
                        else:
                            logger.error("MT5连接重启失败")
                    except Exception as e:
                        logger.error(f"重启MT5连接时出错: {e}", exc_info=True)

                threading.Thread(target=restart_mt5_connection).start()
                flash('MT5连接配置已更新，正在重启连接...', 'info')

            if sl_tp_modified or request.form.get('default_sl_points') or request.form.get('default_tp_points'):
                # 触发MT5模块重新加载配置，并应用止盈止损设置到所有活跃订单
                def update_config_and_apply_sl_tp():
                    mt5_trader.load_config()
                    result = mt5_trader.apply_sl_tp_to_all_positions()
                    if result['success']:
                        logger.info(f"自动应用止盈止损设置成功: {result['message']}")
                    else:
                        logger.error(f"自动应用止盈止损设置失败: {result['message']}")

                # 启动单独的线程来执行，避免阻塞响应
                threading.Thread(target=update_config_and_apply_sl_tp).start()
                flash('设置已应用到所有活跃订单', 'info')
            elif not mt5_config_modified:
                # 仅重新加载配置（如果没有修改MT5配置）
                threading.Thread(target=mt5_trader.load_config).start()
            
            return redirect(url_for('settings'))
        
        except Exception as e:
            logger.error(f"保存设置失败: {e}", exc_info=True)
            flash(f'保存设置失败: {str(e)}', 'danger')
    
    # 读取当前配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 确保bark_notifications配置存在，如果不存在则使用默认值
        if 'bark_notifications' not in config:
            config['bark_notifications'] = {
                'signal_received': True,
                'signal_processing': True,
                'trade_execution': True,
                'trade_closed': True,
                'error': True
            }

        # 获取MT5服务器时间
        server_time = mt5_trader.get_server_time()

        # 获取真实北京时间 (从NTP服务器获取)
        from utils.beijing_time import get_beijing_time
        beijing_time = get_beijing_time()

        return render_template('settings.html',
                               config=config,
                               server_time=server_time.strftime('%Y-%m-%d %H:%M:%S'),
                               beijing_time=beijing_time.strftime('%Y-%m-%d %H:%M:%S'))
    
    except Exception as e:
        logger.error(f"加载设置页面失败: {e}", exc_info=True)
        flash('加载设置失败，请稍后重试', 'danger')
        return render_template('settings.html', config={})

# 路由: 交易报告
@app.route('/reports')
@login_required
def reports():
    try:
        # 获取时间范围参数
        period = request.args.get('period', 'all')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        conn = get_db_connection()
        c = conn.cursor()

        # 构建时间过滤条件 - 只统计通过信号执行的交易
        where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"

        if period == 'today':
            where_clause += " AND DATE(timestamp) = DATE('now')"
        elif period == 'week':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
        elif period == 'month':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
        elif period == 'custom' and start_date and end_date:
            where_clause += f" AND DATE(timestamp) >= '{start_date}' AND DATE(timestamp) <= '{end_date}'"

        # 获取总体统计
        c.execute(f'''
            SELECT COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END) as total_profit,
                   SUM(CASE WHEN profit < 0 THEN profit ELSE 0 END) as total_loss,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
        ''')
        overall_stats_row = c.fetchone()

        overall_stats = {
            'total_orders': 0, 'profitable_orders': 0, 'loss_orders': 0,
            'total_profit': 0.0, 'total_loss': 0.0, 'net_profit': 0.0
        }
        if overall_stats_row:
            overall_stats['total_orders'] = overall_stats_row['total_orders'] if overall_stats_row['total_orders'] is not None else 0
            overall_stats['profitable_orders'] = overall_stats_row['profitable_orders'] if overall_stats_row['profitable_orders'] is not None else 0
            overall_stats['loss_orders'] = overall_stats_row['loss_orders'] if overall_stats_row['loss_orders'] is not None else 0
            overall_stats['total_profit'] = overall_stats_row['total_profit'] if overall_stats_row['total_profit'] is not None else 0.0
            overall_stats['total_loss'] = overall_stats_row['total_loss'] if overall_stats_row['total_loss'] is not None else 0.0
            overall_stats['net_profit'] = overall_stats_row['net_profit'] if overall_stats_row['net_profit'] is not None else 0.0
        
        # 按交易对统计 - 只统计通过信号执行的交易
        c.execute(f'''
            SELECT trading_pair,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
            GROUP BY trading_pair
        ''')
        symbol_stats_rows = c.fetchall()
        symbol_stats = []
        for row in symbol_stats_rows:
            symbol_stats.append({
                'trading_pair': row['trading_pair'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })

        # 按交易类型统计 - 只统计通过信号执行的交易
        c.execute(f'''
            SELECT operation,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
            GROUP BY operation
        ''')
        operation_stats_rows = c.fetchall()
        operation_stats = []
        for row in operation_stats_rows:
            operation_stats.append({
                'operation': row['operation'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })

        # 最近的订单 - 只显示通过信号执行的交易
        c.execute(f'''
            SELECT id, timestamp, trading_pair, operation, volume, profit, status
            FROM orders {where_clause}
            ORDER BY timestamp DESC LIMIT 10
        ''')
        recent_orders = c.fetchall()
        recent_orders_list = [dict(order) for order in recent_orders]
        
        conn.close()
        
        # MT5历史记录现在通过AJAX动态加载，这里只传递空列表
        mt5_history_deals = []
        mt5_history_orders = []
        
        return render_template('reports.html',
                              overall_stats=overall_stats,
                              symbol_stats=symbol_stats,
                              operation_stats=operation_stats,
                              recent_orders=recent_orders,
                              mt5_history_deals=mt5_history_deals,
                              mt5_history_orders=mt5_history_orders)
    
    except Exception as e:
        logger.error(f"加载报告数据失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return render_template('reports.html',
                               overall_stats={'total_orders': 0, 'profitable_orders': 0, 'loss_orders': 0, 'total_profit': 0.0, 'total_loss': 0.0, 'net_profit': 0.0},
                               symbol_stats=[],
                               operation_stats=[],
                               recent_orders=[],
                               mt5_history_deals=[],
                               mt5_history_orders=[])

# 路由: API - 检查MT5环境
@app.route('/api/check_mt5_environment', methods=['GET'])
@login_required
def api_check_mt5_environment():
    try:
        import os
        import subprocess

        # 检查MT5是否已安装
        common_paths = [
            "C:\\Program Files\\MetaTrader 5\\terminal64.exe",
            "C:\\Program Files (x86)\\MetaTrader 5\\terminal64.exe",
            "C:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\*.exe".format(os.getenv('USERNAME', ''))
        ]

        mt5_found = False
        mt5_paths = []

        for path in common_paths:
            if '*' in path:
                # 处理通配符路径
                import glob
                matches = glob.glob(path)
                if matches:
                    mt5_paths.extend(matches)
                    mt5_found = True
            elif os.path.exists(path):
                mt5_paths.append(path)
                mt5_found = True

        # 检查MT5进程是否运行
        mt5_running = False
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq terminal64.exe'],
                                  capture_output=True, text=True, timeout=5)
            mt5_running = 'terminal64.exe' in result.stdout
        except:
            pass

        return jsonify({
            'success': True,
            'mt5_found': mt5_found,
            'mt5_paths': mt5_paths,
            'mt5_running': mt5_running,
            'python_mt5_available': True  # 如果能导入就说明可用
        })

    except Exception as e:
        logger.error(f"检查MT5环境失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 测试MT5连接
@app.route('/api/test_mt5_connection', methods=['POST'])
@login_required
def api_test_mt5_connection():
    try:
        # 获取测试参数
        data = request.get_json()
        mt5_login = data.get('mt5_login', '').strip()
        mt5_password = data.get('mt5_password', '').strip()
        mt5_server = data.get('mt5_server', '').strip()
        mt5_path = data.get('mt5_path', '').strip()

        # 验证必填参数
        if not all([mt5_login, mt5_password, mt5_server]):
            return jsonify({
                'success': False,
                'error': '请填写完整的MT5登录信息'
            })

        # 验证登录ID是否为数字
        try:
            int(mt5_login)
        except ValueError:
            return jsonify({
                'success': False,
                'error': 'MT5登录ID必须是纯数字'
            })

        # 关闭现有连接
        mt5.shutdown()

        # 尝试连接
        try:
            # 首先尝试不指定路径初始化（通常这样就能工作）
            logger.info("测试MT5连接 - 尝试默认初始化")
            init_result = mt5.initialize()

            if not init_result and mt5_path:
                # 如果默认初始化失败，再尝试指定路径
                logger.info(f"测试MT5连接 - 尝试指定路径初始化: {mt5_path}")
                init_result = mt5.initialize(path=mt5_path)

            if not init_result:
                error = mt5.last_error()
                error_msg = f'MT5初始化失败: {error}'

                # 提供详细的错误解释和解决方案
                if isinstance(error, tuple) and len(error) > 0:
                    error_code = error[0]
                    if error_code == -10003:
                        error_msg += '\n\n这个错误通常表示MT5路径问题。解决方案:\n'
                        error_msg += '1. 系统可能已经有MT5在运行，请尝试留空MT5路径字段\n'
                        error_msg += '2. 如果指定了路径，请确保路径正确\n'
                        error_msg += '3. 尝试先手动启动MT5客户端，然后再测试连接\n'
                        error_msg += '4. 重启系统后再试'
                    elif error_code == -6:
                        error_msg += '\n\n可能的解决方案:\n'
                        error_msg += '1. 确保MetaTrader 5客户端已安装并能正常启动\n'
                        error_msg += '2. 检查MT5安装路径是否正确\n'
                        error_msg += '3. 尝试先手动启动MT5客户端，然后再测试连接\n'
                        error_msg += '4. 确保MT5客户端版本与Python库兼容'
                    elif error_code == -1:
                        error_msg += '\n\n解决方案: 检查MT5安装路径是否正确'

                logger.error(f"MT5初始化失败: {error}")
                return jsonify({
                    'success': False,
                    'error': error_msg,
                    'error_code': error[0] if isinstance(error, tuple) else str(error)
                })

            logger.info("MT5初始化成功，开始尝试登录")

            # 尝试登录
            login_result = mt5.login(login=int(mt5_login), password=mt5_password, server=mt5_server)

            if login_result:
                logger.info("MT5登录成功")
                # 获取账户信息验证连接
                account_info = mt5.account_info()
                if account_info:
                    return jsonify({
                        'success': True,
                        'message': '连接测试成功',
                        'account_info': {
                            'login': account_info.login,
                            'server': account_info.server,
                            'name': account_info.name,
                            'balance': account_info.balance,
                            'currency': account_info.currency
                        }
                    })
                else:
                    return jsonify({
                        'success': False,
                        'error': '登录成功但无法获取账户信息'
                    })
            else:
                error = mt5.last_error()
                error_msg = f'登录失败: {error}'

                # 提供详细的登录错误解释
                if isinstance(error, tuple) and len(error) >= 2:
                    if 'Authorization failed' in str(error[1]):
                        error_msg += '\n\n可能的解决方案:\n'
                        error_msg += '1. 检查账号ID、密码是否正确\n'
                        error_msg += '2. 确认服务器名称是否准确（区分大小写）\n'
                        error_msg += '3. 确保账户未被锁定或暂停\n'
                        error_msg += '4. 尝试先在MT5客户端手动登录验证账户信息\n'
                        error_msg += '5. 联系经纪商确认账户状态'
                    elif 'Invalid account' in str(error[1]):
                        error_msg += '\n\n解决方案: 账户不存在，请检查账号ID是否正确'
                    elif 'Common error' in str(error[1]):
                        error_msg += '\n\n解决方案: 通用错误，请检查网络连接和服务器状态'

                logger.error(f"MT5登录失败: {error}")
                return jsonify({
                    'success': False,
                    'error': error_msg,
                    'error_code': error[0] if isinstance(error, tuple) else str(error)
                })

        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'连接测试失败: {str(e)}'
            })
        finally:
            # 恢复原有连接
            try:
                mt5_trader.init_mt5()
            except:
                pass

    except Exception as e:
        logger.error(f"测试MT5连接失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取系统状态
@app.route('/api/status')
def api_status():
    try:
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 获取交易开关状态
        trading_enabled = config.get('enable_trading', False)

        # 检查MT5连接状态
        mt5_connected = mt5.terminal_info() is not None

        return jsonify({
            'success': True,
            'trading_enabled': trading_enabled,
            'mt5_connected': mt5_connected,
            'server_time': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取账户信息
@app.route('/api/account_info')
@login_required
def api_account_info():
    try:
        # 强制初始化MT5连接
        mt5_connection_initialized = mt5_trader.init_mt5()
        logger.info(f"API - MT5连接初始化结果: {mt5_connection_initialized}")
        
        # 检查MT5连接状态
        try:
            terminal_info = mt5.terminal_info()
            mt5_connected = terminal_info is not None
            if mt5_connected:
                logger.info(f"API - MT5已连接，终端名称: {terminal_info.name}")
            else:
                logger.warning("API - MT5未连接，terminal_info返回None")
        except Exception as e:
            logger.error(f"API - 检查MT5连接状态时出错: {e}")
            mt5_connected = False
            
        logger.info(f"API - MT5连接状态: {'已连接' if mt5_connected else '未连接'}")
        
        # 获取账户信息
        account_info = mt5_trader.get_account_info() if mt5_connected else {
            'balance': 0.0,
            'equity': 0.0,
            'profit': 0.0,
            'margin': 0.0,
            'free_margin': 0.0,
            'margin_level': 0.0,
            'currency': 'USD'
        }
        logger.info(f"API - 获取账户信息: {account_info}")
        
        return jsonify({
            'success': True,
            'mt5_connected': mt5_connected,
            'account_info': account_info
        })
    except Exception as e:
        logger.error(f"获取账户信息失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取实时价格
@app.route('/api/prices', methods=['GET'])
@login_required
def get_real_time_prices_api():
    """获取实时价格的API"""
    try:
        # 要监控的交易品种
        symbols = ["XAUUSD", "BTCUSD", "ETHUSD", "GBPJPY", "GBPUSD", "BRENT", "XTIUSD", "DXY"]
        
        # 先检查MT5是否连接
        mt5_connected = False
        try:
            mt5_connected = mt5.terminal_info() is not None
            if not mt5_connected:
                # 尝试初始化连接
                mt5_connected = mt5_trader.init_mt5()
        except Exception as e:
            logger.error(f"检查MT5连接失败: {e}", exc_info=True)
        
        if not mt5_connected:
            # MT5未连接，返回错误信息
            # 为所有交易品种返回错误信息
            error_data = {}
            for symbol in symbols:
                error_data[symbol] = {
                    "bid": 0.0,
                    "ask": 0.0,
                    "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "spread": 0.0,
                    "error": "MT5未连接"
                }

            return jsonify({
                "status": "success",  # 仍然返回成功状态码，但包含错误信息
                "data": error_data,
                "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
        
        # 获取实时价格
        prices = mt5_trader.get_real_time_prices(symbols)
        return jsonify({"status": "success", "data": prices, "time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')})
    except Exception as e:
        logger.error(f"获取实时价格时出错: {e}", exc_info=True)
        return jsonify({"status": "error", "message": str(e)})

# 路由: API - 获取盈亏历史数据用于图表
@app.route('/api/profit_history')
@login_required
def api_profit_history():
    try:
        # 获取时间段参数
        period = request.args.get('period', 'all')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        conn = get_db_connection()
        c = conn.cursor()

        # 根据时间段筛选 - 只统计通过信号执行的交易
        where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"
        if period == 'today':
            where_clause += " AND DATE(timestamp) = DATE('now')"
        elif period == 'week':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
        elif period == 'month':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
        elif period == 'custom' and start_date and end_date:
            where_clause += f" AND DATE(timestamp) >= '{start_date}' AND DATE(timestamp) <= '{end_date}'"

        # 获取已平仓订单
        c.execute(f'''
            SELECT timestamp, profit
            FROM orders
            {where_clause}
            ORDER BY timestamp ASC
        ''')

        orders = c.fetchall()

        conn.close()

        # 按时间累计盈亏
        dates = []
        profits = []
        accumulated_profit = 0

        for order in orders:
            if order['profit'] is not None:  # 确保profit不为None
                dates.append(order['timestamp'].split('T')[0])  # 只取日期部分
                accumulated_profit += float(order['profit'])
                profits.append(accumulated_profit)

        # 如果没有数据，提供默认值
        if not dates:
            dates = [datetime.now().strftime('%Y-%m-%d')]
            profits = [0]

        return jsonify({
            'success': True,
            'dates': dates,
            'profits': profits
        })
    except Exception as e:
        logger.error(f"获取盈亏历史数据失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: API - 获取MT5历史记录（支持时间范围过滤和分页）
@app.route('/api/mt5_history')
@login_required
def api_mt5_history():
    try:
        # 获取参数
        record_type = request.args.get('type', 'deals')  # deals 或 orders
        period = request.args.get('period', 'all')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 30, type=int)

        # 初始化MT5连接
        mt5_initialized = mt5_trader.init_mt5()
        if not mt5_initialized:
            return jsonify({
                'success': False,
                'error': 'MT5未连接',
                'data': [],
                'total': 0,
                'page': page,
                'per_page': per_page,
                'total_pages': 0
            })

        # 计算时间范围
        end_datetime = datetime.now()

        if period == 'today':
            start_datetime = datetime.combine(datetime.today(), datetime.min.time())
        elif period == 'week':
            start_datetime = end_datetime - timedelta(days=7)
        elif period == 'month':
            start_datetime = end_datetime - timedelta(days=30)
        elif period == 'custom' and start_date and end_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
        else:
            # 默认获取最近30天的数据
            start_datetime = end_datetime - timedelta(days=30)

        # 获取MT5历史记录
        if record_type == 'deals':
            all_records = mt5_trader.get_history_deals(
                from_date=start_datetime,
                to_date=end_datetime,
                mt5_initialized=mt5_initialized,
                init_mt5=mt5_trader.init_mt5
            )
            # 只显示买入/卖出交易，排除余额变动等
            all_records = [deal for deal in all_records if deal['type'] in [0, 1]]
        else:
            all_records = mt5_trader.get_history_orders(
                from_date=start_datetime,
                to_date=end_datetime,
                mt5_initialized=mt5_initialized,
                init_mt5=mt5_trader.init_mt5
            )

        # 按时间倒序排序 - 处理混合的时间戳格式
        def get_record_time_for_sort(record):
            """获取用于排序的记录时间，统一转换为datetime对象"""
            try:
                time_value = record.get('time', '')
                if isinstance(time_value, str):
                    if not time_value:  # 空字符串
                        return datetime.min
                    # 处理ISO格式字符串
                    if time_value.endswith('Z'):
                        time_value = time_value.replace('Z', '+00:00')
                    return datetime.fromisoformat(time_value)
                elif isinstance(time_value, (int, float)):
                    # 处理Unix时间戳
                    return datetime.fromtimestamp(time_value)
                else:
                    # 如果是其他类型，返回最小时间作为默认值
                    logger.warning(f"未知的记录时间格式: {type(time_value)} - {time_value}")
                    return datetime.min
            except Exception as e:
                logger.error(f"解析记录时间失败: {e}, time: {record.get('time')}")
                return datetime.min  # 返回最小时间作为默认值

        all_records.sort(key=get_record_time_for_sort, reverse=True)

        # 计算分页
        total = len(all_records)
        total_pages = (total + per_page - 1) // per_page if per_page > 0 else 1
        start_index = (page - 1) * per_page
        end_index = start_index + per_page
        records = all_records[start_index:end_index]

        return jsonify({
            'success': True,
            'data': records,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': total_pages,
            'period': period,
            'start_date': start_datetime.strftime('%Y-%m-%d'),
            'end_date': end_datetime.strftime('%Y-%m-%d')
        })

    except Exception as e:
        logger.error(f"获取MT5历史记录失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e),
            'data': [],
            'total': 0,
            'page': page,
            'per_page': per_page,
            'total_pages': 0
        })

# 路由: API - 获取报告统计数据
@app.route('/api/report_stats')
@login_required
def api_report_stats():
    try:
        # 获取时间范围参数
        period = request.args.get('period', 'all')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        conn = get_db_connection()
        c = conn.cursor()

        # 构建时间过滤条件 - 只统计通过信号执行的交易
        where_clause = "WHERE status = 'closed' AND signal_id IS NOT NULL"

        if period == 'today':
            where_clause += " AND DATE(timestamp) = DATE('now')"
        elif period == 'week':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-7 days')"
        elif period == 'month':
            where_clause += " AND DATE(timestamp) >= DATE('now', '-30 days')"
        elif period == 'custom' and start_date and end_date:
            where_clause += f" AND DATE(timestamp) >= '{start_date}' AND DATE(timestamp) <= '{end_date}'"

        # 获取总体统计
        c.execute(f'''
            SELECT COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(CASE WHEN profit > 0 THEN profit ELSE 0 END) as total_profit,
                   SUM(CASE WHEN profit < 0 THEN profit ELSE 0 END) as total_loss,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
        ''')
        overall_stats_row = c.fetchone()

        overall_stats = {
            'total_orders': 0, 'profitable_orders': 0, 'loss_orders': 0,
            'total_profit': 0.0, 'total_loss': 0.0, 'net_profit': 0.0
        }

        if overall_stats_row:
            overall_stats['total_orders'] = overall_stats_row['total_orders'] if overall_stats_row['total_orders'] is not None else 0
            overall_stats['profitable_orders'] = overall_stats_row['profitable_orders'] if overall_stats_row['profitable_orders'] is not None else 0
            overall_stats['loss_orders'] = overall_stats_row['loss_orders'] if overall_stats_row['loss_orders'] is not None else 0
            overall_stats['total_profit'] = overall_stats_row['total_profit'] if overall_stats_row['total_profit'] is not None else 0.0
            overall_stats['total_loss'] = overall_stats_row['total_loss'] if overall_stats_row['total_loss'] is not None else 0.0
            overall_stats['net_profit'] = overall_stats_row['net_profit'] if overall_stats_row['net_profit'] is not None else 0.0

        # 按交易对统计
        c.execute(f'''
            SELECT trading_pair,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
            GROUP BY trading_pair
        ''')
        symbol_stats_rows = c.fetchall()
        symbol_stats = []
        for row in symbol_stats_rows:
            symbol_stats.append({
                'trading_pair': row['trading_pair'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })

        # 按交易类型统计
        c.execute(f'''
            SELECT operation,
                   COUNT(*) as total_orders,
                   SUM(CASE WHEN profit > 0 THEN 1 ELSE 0 END) as profitable_orders,
                   SUM(CASE WHEN profit < 0 THEN 1 ELSE 0 END) as loss_orders,
                   SUM(profit) as net_profit
            FROM orders {where_clause}
            GROUP BY operation
        ''')
        operation_stats_rows = c.fetchall()
        operation_stats = []
        for row in operation_stats_rows:
            operation_stats.append({
                'operation': row['operation'],
                'total_orders': row['total_orders'] if row['total_orders'] is not None else 0,
                'profitable_orders': row['profitable_orders'] if row['profitable_orders'] is not None else 0,
                'loss_orders': row['loss_orders'] if row['loss_orders'] is not None else 0,
                'net_profit': row['net_profit'] if row['net_profit'] is not None else 0.0
            })

        conn.close()

        return jsonify({
            'success': True,
            'overall_stats': overall_stats,
            'symbol_stats': symbol_stats,
            'operation_stats': operation_stats
        })
    except Exception as e:
        logger.error(f"获取报告统计数据失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 路由: 导出报告
@app.route('/export_report')
@login_required
def export_report():
    try:
        import csv
        import io
        from flask import Response
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取所有已平仓订单
        c.execute('''
            SELECT id, timestamp, ticket, trading_pair, operation, volume, price, close_price,
                   profit, close_time, signal_id
            FROM orders WHERE status = 'closed'
            ORDER BY timestamp DESC
        ''')
        
        orders = c.fetchall()
        conn.close()
        
        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['订单ID', '开仓时间', '订单号', '交易对', '交易方向', '交易量', 
                        '开仓价', '平仓价', '盈亏', '平仓时间', '关联信号ID'])
        
        # 写入数据
        for order in orders:
            row = [
                order['id'],
                order['timestamp'].replace('T', ' ').split('.')[0],
                order['ticket'],
                order['trading_pair'],
                '买入' if order['operation'] == 'buy' else '卖出',
                order['volume'],
                order['price'],
                order['close_price'],
                order['profit'],
                order['close_time'].replace('T', ' ').split('.')[0] if order['close_time'] else '-',
                order['signal_id']
            ]
            writer.writerow(row)
        
        # 设置响应头，使浏览器将其作为文件下载
        output.seek(0)
        date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        return Response(
            output,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment;filename=trading_report_{date_str}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )
    
    except Exception as e:
        logger.error(f"导出报告失败: {e}", exc_info=True)
        flash('导出报告失败，请稍后重试', 'danger')
        return redirect(url_for('reports'))

# 路由: 导出信号CSV
@app.route('/export_signals_csv')
@login_required
def export_signals_csv():
    try:
        import csv
        import io
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 获取所有信号
        c.execute('''
            SELECT id, timestamp, trading_pair, signal_type, interval, mrc_event, processed, 
                   process_time, order_ticket, order_result
            FROM signals ORDER BY timestamp DESC
        ''')
        
        signals = c.fetchall()
        conn.close()
        
        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(['信号ID', '时间戳', '交易对', '信号类型', '时间周期', 'MRC事件', 
                        '是否已处理', '处理时间', '订单号', '订单结果'])
        
        # 写入数据
        for signal_row in signals:  # Renamed to avoid conflict
            row = [
                signal_row['id'],
                signal_row['timestamp'].replace('T', ' ').split('.')[0],
                signal_row['trading_pair'],
                signal_row['signal_type'],
                signal_row['interval'],
                signal_row['mrc_event'],
                '是' if signal_row['processed'] == 1 else '否',
                signal_row['process_time'].replace('T', ' ').split('.')[0] if signal_row['process_time'] else '-',
                signal_row['order_ticket'],
                signal_row['order_result']
            ]
            writer.writerow(row)
        
        # 设置响应头，使浏览器将其作为文件下载
        output.seek(0)
        date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
        return Response(
            output,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment;filename=signals_export_{date_str}.csv',
                'Content-Type': 'text/csv; charset=utf-8'
            }
        )
    
    except Exception as e:
        logger.error(f"导出信号CSV失败: {e}", exc_info=True)
        flash('导出信号CSV失败，请稍后重试', 'danger')
        return redirect(url_for('signals'))

# 路由: API - 获取所有服务的状态
@app.route('/api/services_status')
@login_required
def api_services_status():
    try:
        # 检查MT5连接状态
        try:
            terminal_info = mt5.terminal_info()
            mt5_connected = terminal_info is not None
        except Exception as e:
            logger.error(f"API - 检查MT5连接状态时出错: {e}")
            mt5_connected = False
        
        # 检查信号接收服务状态
        signal_receiver_running = check_signal_receiver_status()
        
        # 读取配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 获取交易开关状态
        trading_enabled = config.get('enable_trading', False)
        
        return jsonify({
            'success': True,
            'trading_enabled': trading_enabled,
            'mt5_connected': mt5_connected,
            'signal_receiver_running': signal_receiver_running,
            'server_time': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API路由已移至api_routes.py模块

def check_signal_receiver_status():
    """
    检查信号接收服务(signal_receiver)是否在运行
    
    返回:
        bool: 服务是否在运行
        
    注意:
        我们可以通过多种方式检查服务状态：
        1. 检查进程是否存在
        2. 尝试向服务发送HTTP请求检查响应
        3. 检查服务日志文件的最后修改时间
        
        这里使用第3种方法，检查日志文件的最后更新时间，如果最近10分钟内有更新，就认为服务在运行
    """
    try:
        # 检查信号接收器日志文件最后修改时间
        log_path = "logs/signal_receiver.log"
        if not os.path.exists(log_path):
            logger.warning(f"信号接收服务日志文件不存在: {log_path}")
            return False
        
        # 获取日志文件最后修改时间
        last_modified_time = os.path.getmtime(log_path)
        current_time = time.time()
        
        # 如果日志在最近10分钟内有更新，认为服务在运行
        if current_time - last_modified_time < 600:  # 10分钟 = 600秒
            return True
        
        # 如果日志文件太旧，尝试向服务发送请求检测是否在运行
        try:
            # 使用requests库发送请求，如果能连接，说明服务在运行
            import socket
            
            # 尝试创建一个socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)  # 设置超时时间为1秒
            
            # 获取配置中的signal_receiver端口
            signal_receiver_port = config.get('server_port', 9999)
            result = sock.connect_ex(('127.0.0.1', signal_receiver_port))
            sock.close()
            
            # 如果端口开放，说明服务在运行
            if result == 0:
                logger.info(f"信号接收服务端口 {signal_receiver_port} 可访问")
                return True
        except Exception as e:
            logger.error(f"检查信号接收服务端口时出错: {e}")
        
        logger.warning("信号接收服务可能不在运行，日志文件最后修改时间超过10分钟")
        return False
    except Exception as e:
        logger.error(f"检查信号接收服务状态时出错: {e}", exc_info=True)
        return False

# 路由: 平仓所有持仓
@app.route('/close_all_positions', methods=['POST'])
@login_required
def close_all_positions():
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接，无法执行平仓操作'})
        
        # 执行平仓所有持仓操作
        result = mt5_trader.close_all_positions()
        
        # 记录操作日志
        if result.get('success', False):
            logger.info(f"平仓所有持仓操作完成: {result.get('message', '')}")
            # 如果平仓成功，可能需要更新数据库中的订单状态
            if result.get('closed_count', 0) > 0:
                try:
                    conn = get_db_connection()
                    conn.execute('''
                        UPDATE orders
                        SET status = 'closed', close_time = ?
                        WHERE status IN ('open', 'partially_closed')
                    ''', (datetime.now().isoformat(),))
                    conn.commit()
                    conn.close()
                except Exception as e:
                    logger.error(f"更新数据库中的订单状态失败: {e}", exc_info=True)
        else:
            logger.error(f"平仓所有持仓失败: {result.get('message', '未知错误')}")
        
        return jsonify(result)

    except Exception as e:
        logger.error(f"平仓所有持仓操作异常: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'操作异常: {str(e)}'})

# 路由: 平仓所有盈利持仓
@app.route('/close_profitable_positions', methods=['POST'])
@login_required
def close_profitable_positions():
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接，无法执行平仓操作'})

        # 执行平仓盈利持仓操作
        result = mt5_trader.close_profitable_positions()

        # 记录操作日志
        if result.get('success', False):
            logger.info(f"平仓盈利持仓操作完成: {result.get('message', '')}")
            # 如果平仓成功，可能需要更新数据库中的订单状态
            if result.get('closed_count', 0) > 0:
                try:
                    conn = get_db_connection()
                    # 这里我们不能简单地更新所有订单，因为只平仓了盈利的订单
                    # 实际的订单状态更新应该在 mt5_trader.close_position 中处理
                    conn.close()
                except Exception as e:
                    logger.error(f"数据库操作失败: {e}", exc_info=True)
        else:
            logger.error(f"平仓盈利持仓失败: {result.get('message', '未知错误')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"平仓盈利持仓操作异常: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'操作异常: {str(e)}'})

# 路由: 同步已平仓订单
@app.route('/sync_closed_orders', methods=['POST'])
@login_required
def sync_closed_orders():
    try:
        logger.info("收到同步已平仓订单请求")

        # 强制初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5连接失败'})

        # 获取最近30天的已平仓持仓记录
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)

        logger.info(f"查询时间范围: {start_date} 到 {end_date}")

        # 使用专门的函数获取已平仓持仓记录
        closed_deals = mt5_trader.get_closed_positions(
            from_date=start_date,
            to_date=end_date,
            mt5_initialized=mt5_connected,
            init_mt5=mt5_trader.init_mt5
        )

        logger.info(f"从MT5获取到 {len(closed_deals)} 条已平仓持仓记录")

        if len(closed_deals) == 0:
            return jsonify({'success': True, 'message': '没有找到已平仓的交易记录'})

        # 更新数据库中对应的订单状态
        conn = get_db_connection()
        c = conn.cursor()
        updated_count = 0
        skipped_count = 0
        not_found_count = 0

        for deal in closed_deals:
            try:
                position_id = deal.get('position_id')
                if not position_id:
                    logger.warning(f"交易记录缺少position_id: {deal}")
                    continue

                # 根据position_id查找对应的订单
                c.execute('SELECT id, status, ticket FROM orders WHERE ticket = ?', (position_id,))
                order = c.fetchone()

                if not order:
                    # 尝试通过其他方式查找订单（如果position_id不匹配ticket）
                    c.execute('SELECT id, status, ticket FROM orders WHERE trading_pair = ? AND volume = ? AND price = ?',
                             (deal.get('symbol'), deal.get('volume'), deal.get('price')))
                    potential_orders = c.fetchall()

                    if potential_orders:
                        logger.info(f"通过交易对、数量、价格找到 {len(potential_orders)} 个可能的订单")
                        order = potential_orders[0]  # 取第一个匹配的订单
                    else:
                        not_found_count += 1
                        logger.warning(f"未找到对应的订单记录: position_id={position_id}, symbol={deal.get('symbol')}")
                        continue

                if order['status'] == 'closed':
                    skipped_count += 1
                    logger.debug(f"订单 {position_id} 已经是已平仓状态，跳过")
                    continue

                # 更新订单状态为已平仓
                close_time = deal['time']
                close_price = deal['price']
                profit = deal['profit']

                c.execute('''
                    UPDATE orders
                    SET status = 'closed',
                        close_time = ?,
                        profit = ?,
                        close_price = ?
                    WHERE id = ?
                ''', (close_time, profit, close_price, order['id']))

                updated_count += 1
                logger.info(f"更新订单 {position_id} 状态为已平仓，盈亏: {profit}")

            except Exception as e:
                logger.error(f"更新订单 {deal.get('position_id', 'Unknown')} 失败: {e}")

        conn.commit()
        conn.close()

        message = f"同步完成: 更新 {updated_count} 个订单，跳过 {skipped_count} 个已平仓订单，未找到 {not_found_count} 个订单"
        logger.info(message)

        return jsonify({
            'success': True,
            'message': message,
            'updated_count': updated_count,
            'skipped_count': skipped_count,
            'not_found_count': not_found_count,
            'total_closed_deals': len(closed_deals)
        })

    except Exception as e:
        logger.error(f"同步已平仓订单失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'同步失败: {str(e)}'})

# 路由: 通过MT5订单号直接查看订单
@app.route('/position/<int:ticket>')
@login_required
def position_detail(ticket):
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            flash('MetaTrader5连接失败，无法获取订单数据', 'warning')
            return redirect(url_for('orders'))
        
        # 获取所有MT5持仓
        mt5_positions = mt5_trader.get_all_positions()
        position = next((pos for pos in mt5_positions if pos['ticket'] == ticket), None)
        
        if not position:
            flash(f'未找到票号为 {ticket} 的持仓', 'danger')
            return redirect(url_for('orders'))
        
        # 格式化持仓信息
        open_time = datetime.fromtimestamp(position['time'])
        duration = datetime.now() - open_time
        
        order = {
            'id': None,  # 数据库ID为空
            'ticket': position['ticket'],
            'trading_pair': position['symbol'],
            'operation': position['type_str'],
            'volume': position['volume'],
            'price': position['price_open'],
            'price_current': position['price_current'], 
            'sl': position['sl'],
            'tp': position['tp'],
            'status': 'open',
            'profit': position['profit'],
            'swap': position['swap'],
            'comment': position['comment'],
            'magic': position['magic'],
            'timestamp': position['time'],
            'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': f"{duration.days}天 {duration.seconds//3600}小时",
            'from_mt5': True
        }
        
        # 尝试查找是否有关联的数据库订单和信号
        conn = get_db_connection()
        c = conn.cursor()
        
        c.execute('SELECT * FROM orders WHERE ticket = ?', (ticket,))
        db_order = c.fetchone()
        signal = None
        
        if db_order and db_order['signal_id']:
            c.execute('SELECT * FROM signals WHERE id = ?', (db_order['signal_id'],))
            signal = c.fetchone()
            
            # 补充数据库中的数据
            order['id'] = db_order['id']
            order['signal_id'] = db_order['signal_id']
        
        conn.close()
        
        return render_template('order_detail.html', order=order, signal=signal, from_mt5=True)
    
    except Exception as e:
        logger.error(f"加载MT5持仓详情失败: {e}", exc_info=True)
        flash('加载数据失败，请稍后重试', 'danger')
        return redirect(url_for('orders'))

# 路由: API - 获取持仓详情
@app.route('/api/position/<int:ticket>')
@login_required
def api_position_detail(ticket):
    """获取指定持仓的实时详情"""
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接'})
        
        # 获取所有MT5持仓
        mt5_positions = mt5_trader.get_all_positions()
        position = next((pos for pos in mt5_positions if pos['ticket'] == ticket), None)
        
        if not position:
            return jsonify({'success': False, 'message': f'未找到票号为 {ticket} 的持仓'})
        
        # 格式化持仓信息
        open_time = datetime.fromtimestamp(position['time'])
        duration = datetime.now() - open_time
        
        position_info = {
            'ticket': position['ticket'],
            'symbol': position['symbol'],
            'type': position['type'],
            'type_str': position['type_str'],
            'volume': position['volume'],
            'price_open': position['price_open'],
            'price_current': position['price_current'],
            'sl': position['sl'],
            'tp': position['tp'],
            'profit': position['profit'],
            'swap': position['swap'],
            'magic': position['magic'],
            'comment': position['comment'],
            'time': position['time'],
            'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': f"{duration.days}天 {duration.seconds//3600}小时 {(duration.seconds%3600)//60}分钟"
        }
        
        return jsonify({
            'success': True,
            'position': position_info
        })
    
    except Exception as e:
        logger.error(f"获取持仓详情失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f"获取持仓详情失败: {str(e)}"})

# 路由: API - 获取活跃订单
@app.route('/api/active_orders')
@login_required
def api_active_orders():
    """获取所有活跃订单的实时数据"""
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接', 'orders': []})

        # 获取所有MT5持仓
        mt5_positions = mt5_trader.get_all_positions()

        # 转换为前端需要的格式
        active_orders = []
        for position in mt5_positions:
            # 计算持仓时长
            open_time = datetime.fromtimestamp(position.get('time', 0))
            duration = datetime.now() - open_time if open_time else None

            formatted_position = {
                'id': None,
                'ticket': position['ticket'],
                'trading_pair': position['symbol'],
                'operation': position['type_str'],
                'volume': position['volume'],
                'price': position['price_open'],
                'current_price': position['price_current'],
                'sl': position['sl'] if position['sl'] != 0.0 else None,
                'tp': position['tp'] if position['tp'] != 0.0 else None,
                'profit': position['profit'],
                'swap': position.get('swap', 0.0),
                'comment': position.get('comment', ''),
                'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S') if open_time else 'Unknown',
                'duration': f"{duration.days}天 {duration.seconds//3600}小时" if duration else 'Unknown',
                'from_mt5': True
            }
            active_orders.append(formatted_position)

        return jsonify({
            'success': True,
            'orders': active_orders,
            'count': len(active_orders),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        logger.error(f"获取活跃订单失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取活跃订单失败: {str(e)}', 'orders': []})

# 路由: API - 获取所有持仓
@app.route('/api/positions')
@login_required
def api_positions():
    """获取所有持仓的实时详情"""
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接'})

        # 获取所有MT5持仓
        mt5_positions = mt5_trader.get_all_positions()
        
        # 格式化持仓信息
        positions_info = []
        for position in mt5_positions:
            open_time = datetime.fromtimestamp(position['time'])
            duration = datetime.now() - open_time
            
            position_info = {
                'ticket': position['ticket'],
                'symbol': position['symbol'],
                'type': position['type'],
                'type_str': position['type_str'],
                'volume': position['volume'],
                'price_open': position['price_open'],
                'price_current': position['price_current'],
                'sl': position['sl'],
                'tp': position['tp'],
                'profit': position['profit'],
                'swap': position['swap'],
                'magic': position['magic'],
                'comment': position['comment'],
                'time': position['time'],
                'time_formatted': open_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': f"{duration.days}天 {duration.seconds//3600}小时 {(duration.seconds%3600)//60}分钟"
            }
            positions_info.append(position_info)
        
        return jsonify({
            'success': True,
            'positions': positions_info
        })
    
    except Exception as e:
        logger.error(f"获取所有持仓失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f"获取所有持仓失败: {str(e)}"})

@app.route('/delete_signal/<int:signal_id>', methods=['POST'])
@login_required
def delete_signal(signal_id):
    try:
        conn = get_db_connection()
        
        # 检查信号是否存在
        c = conn.cursor()
        c.execute('SELECT processed, order_ticket FROM signals WHERE id = ?', (signal_id,))
        signal = c.fetchone()
        
        if not signal:
            conn.close()
            return jsonify({'success': False, 'message': '信号不存在'})
        
        # 检查信号是否已处理且已关联到订单
        if signal['processed'] == 1 and signal['order_ticket'] is not None:
            # 这里可以增加额外处理逻辑，比如记录日志或通知用户
            logger.warning(f"删除已处理的信号 ID: {signal_id}, 该信号已关联订单 {signal['order_ticket']}")
            
        # 执行删除操作
        c.execute('DELETE FROM signals WHERE id = ?', (signal_id,))
        conn.commit()
        conn.close()
        
        logger.info(f"成功删除信号 ID: {signal_id}")
        return jsonify({'success': True, 'message': '信号已删除'})
        
    except Exception as e:
        logger.error(f"删除信号失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'删除信号时出错: {str(e)}'})

@app.route('/batch_delete_signals', methods=['POST'])
@login_required
def batch_delete_signals():
    try:
        data = request.json
        if not data or 'signal_ids' not in data or not data['signal_ids']:
            return jsonify({'success': False, 'message': '未提供有效的信号ID'})
        
        signal_ids = data['signal_ids']
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 记录被处理过的信号
        c.execute('SELECT id, processed, order_ticket FROM signals WHERE id IN ({})'.format(
            ','.join(['?' for _ in signal_ids])), signal_ids)
        processed_signals = [row for row in c.fetchall() if row['processed'] == 1 and row['order_ticket'] is not None]
        
        if processed_signals:
            logger.warning(f"批量删除中包含 {len(processed_signals)} 个已处理的信号")
        
        # 执行批量删除
        placeholders = ', '.join(['?' for _ in signal_ids])
        c.execute(f'DELETE FROM signals WHERE id IN ({placeholders})', signal_ids)
        conn.commit()
        
        deleted_count = c.rowcount
        conn.close()
        
        logger.info(f"成功批量删除 {deleted_count} 个信号")
        return jsonify({
            'success': True, 
            'deleted_count': deleted_count,
            'message': f'已删除 {deleted_count} 个信号'
        })
        
    except Exception as e:
        logger.error(f"批量删除信号失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'批量删除信号时出错: {str(e)}'})

# 路由: API - 获取总浮动盈亏
@app.route('/api/total_floating_pl')
@login_required
def api_total_floating_pl():
    """获取总浮动盈亏"""
    try:
        # 初始化MT5连接
        mt5_connected = mt5_trader.init_mt5()
        if not mt5_connected:
            return jsonify({'success': False, 'message': 'MT5未连接'})
        
        # 获取所有持仓
        positions = mt5_trader.get_all_positions()
        total_floating_pl = sum(position.get('profit', 0) for position in positions)
        
        return jsonify({
            'success': True,
            'total_floating_pl': total_floating_pl,
            'position_count': len(positions)
        })
    except Exception as e:
        logger.error(f"获取总浮动盈亏失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取总浮动盈亏失败: {str(e)}'})

# 路由: API - 获取/设置总浮动盈亏规则
@app.route('/api/profit_loss_rules/total', methods=['GET', 'POST'])
@login_required
def api_total_profit_loss_rule():
    """获取或设置总浮动盈亏规则"""
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        if request.method == 'GET':
            # 获取总规则
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'total' LIMIT 1")
            rule = c.fetchone()
            
            if rule:
                rule_dict = dict(rule)
                conn.close()
                return jsonify({'success': True, 'rule': rule_dict})
            else:
                # 如果没有总规则，创建默认规则
                c.execute('''
                    INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold)
                    VALUES ('total', 0, 1000.0, -500.0)
                ''')
                conn.commit()
                
                # 获取刚创建的规则
                c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'total' LIMIT 1")
                rule = c.fetchone()
                rule_dict = dict(rule)
                conn.close()
                return jsonify({'success': True, 'rule': rule_dict})
        
        elif request.method == 'POST':
            # 设置总规则
            data = request.json
            if not data:
                conn.close()
                return jsonify({'success': False, 'message': '无效的请求数据'})
            
            enabled = data.get('enabled', 0)
            profit_threshold = data.get('profit_threshold')
            loss_threshold = data.get('loss_threshold')
            
            # 验证数据
            if profit_threshold is None or loss_threshold is None:
                conn.close()
                return jsonify({'success': False, 'message': '盈利阈值和亏损阈值不能为空'})
            
            # 更新或插入规则
            c.execute('''
                INSERT OR REPLACE INTO profit_loss_rules 
                (id, rule_type, enabled, profit_threshold, loss_threshold, updated_at)
                VALUES (
                    (SELECT id FROM profit_loss_rules WHERE rule_type = 'total' LIMIT 1),
                    'total', ?, ?, ?, CURRENT_TIMESTAMP
                )
            ''', (enabled, profit_threshold, loss_threshold))
            
            conn.commit()
            
            # 获取更新后的规则
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'total' LIMIT 1")
            rule = c.fetchone()
            rule_dict = dict(rule)
            conn.close()
            
            logger.info(f"总浮动盈亏规则已更新: 启用={enabled}, 盈利阈值={profit_threshold}, 亏损阈值={loss_threshold}")
            return jsonify({'success': True, 'rule': rule_dict, 'message': '总规则设置成功'})
            
    except Exception as e:
        logger.error(f"处理总浮动盈亏规则失败: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'success': False, 'message': f'处理总规则失败: {str(e)}'})

# 路由: API - 获取单个订单浮动盈亏规则
@app.route('/api/profit_loss_rules/individual/<int:ticket>')
@login_required
def api_individual_profit_loss_rule(ticket):
    """获取指定订单的浮动盈亏规则"""
    try:
        conn = get_db_connection()
        c = conn.cursor()
        
        c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'individual' AND ticket = ?", (ticket,))
        rule = c.fetchone()
        
        if rule:
            rule_dict = dict(rule)
            conn.close()
            return jsonify({'success': True, 'rule': rule_dict})
        else:
            conn.close()
            return jsonify({'success': True, 'rule': None})  # 没有规则返回None
            
    except Exception as e:
        logger.error(f"获取订单{ticket}的浮动盈亏规则失败: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'success': False, 'message': f'获取订单规则失败: {str(e)}'})

# 路由: API - 设置单个订单浮动盈亏规则
@app.route('/api/profit_loss_rules/individual', methods=['POST'])
@login_required
def api_set_individual_profit_loss_rule():
    """设置单个订单的浮动盈亏规则"""
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})
        
        ticket = data.get('ticket')
        enabled = data.get('enabled', 0)
        profit_threshold = data.get('profit_threshold')
        loss_threshold = data.get('loss_threshold')
        
        # 验证数据
        if ticket is None:
            return jsonify({'success': False, 'message': '订单号不能为空'})
        
        if profit_threshold is None or loss_threshold is None:
            return jsonify({'success': False, 'message': '盈利阈值和亏损阈值不能为空'})
        
        conn = get_db_connection()
        c = conn.cursor()
        
        # 检查是否已存在该订单的规则
        c.execute("SELECT id FROM profit_loss_rules WHERE rule_type = 'individual' AND ticket = ?", (ticket,))
        existing_rule = c.fetchone()
        
        if existing_rule:
            # 更新现有规则
            c.execute('''
                UPDATE profit_loss_rules 
                SET enabled = ?, profit_threshold = ?, loss_threshold = ?, updated_at = CURRENT_TIMESTAMP
                WHERE rule_type = 'individual' AND ticket = ?
            ''', (enabled, profit_threshold, loss_threshold, ticket))
        else:
            # 插入新规则
            c.execute('''
                INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold, ticket)
                VALUES ('individual', ?, ?, ?, ?)
            ''', (enabled, profit_threshold, loss_threshold, ticket))
        
        conn.commit()
        
        # 获取更新后的规则
        c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'individual' AND ticket = ?", (ticket,))
        rule = c.fetchone()
        rule_dict = dict(rule)
        conn.close()
        
        logger.info(f"订单{ticket}的浮动盈亏规则已设置: 启用={enabled}, 盈利阈值={profit_threshold}, 亏损阈值={loss_threshold}")
        return jsonify({'success': True, 'rule': rule_dict, 'message': '订单规则设置成功'})
        
    except Exception as e:
        logger.error(f"设置单个订单浮动盈亏规则失败: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'success': False, 'message': f'设置订单规则失败: {str(e)}'})

# 路由: API - 获取/设置全局止盈止损规则
@app.route('/api/profit_loss_rules/global_sl_tp', methods=['GET', 'POST'])
@login_required
def api_global_sl_tp_rule():
    """获取或设置全局止盈止损规则"""
    try:
        conn = get_db_connection()
        c = conn.cursor()

        if request.method == 'GET':
            # 获取全局止盈止损规则
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'global_sl_tp' LIMIT 1")
            rule = c.fetchone()

            if rule:
                rule_dict = dict(rule)
                conn.close()
                return jsonify({'success': True, 'rule': rule_dict})
            else:
                # 如果没有全局止盈止损规则，创建默认规则
                c.execute('''
                    INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold)
                    VALUES ('global_sl_tp', 0, 100.0, -50.0)
                ''')
                conn.commit()

                # 获取刚创建的规则
                c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'global_sl_tp' LIMIT 1")
                rule = c.fetchone()
                rule_dict = dict(rule)
                conn.close()
                return jsonify({'success': True, 'rule': rule_dict})

        elif request.method == 'POST':
            # 设置全局止盈止损规则
            data = request.json
            if not data:
                conn.close()
                return jsonify({'success': False, 'message': '无效的请求数据'})

            enabled = data.get('enabled', 0)
            profit_threshold = data.get('profit_threshold')
            loss_threshold = data.get('loss_threshold')

            # 验证数据
            if profit_threshold is None or loss_threshold is None:
                conn.close()
                return jsonify({'success': False, 'message': '止盈金额和止损金额不能为空'})

            # 检查是否已存在全局止盈止损规则
            c.execute("SELECT id FROM profit_loss_rules WHERE rule_type = 'global_sl_tp'")
            existing_rule = c.fetchone()

            if existing_rule:
                # 更新现有规则
                c.execute('''
                    UPDATE profit_loss_rules
                    SET enabled = ?, profit_threshold = ?, loss_threshold = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE rule_type = 'global_sl_tp'
                ''', (enabled, profit_threshold, loss_threshold))
            else:
                # 插入新规则
                c.execute('''
                    INSERT INTO profit_loss_rules (rule_type, enabled, profit_threshold, loss_threshold)
                    VALUES ('global_sl_tp', ?, ?, ?)
                ''', (enabled, profit_threshold, loss_threshold))

            conn.commit()

            # 获取更新后的规则
            c.execute("SELECT * FROM profit_loss_rules WHERE rule_type = 'global_sl_tp' LIMIT 1")
            rule = c.fetchone()
            rule_dict = dict(rule)
            conn.close()

            logger.info(f"全局止盈止损规则已更新: 启用={enabled}, 止盈金额={profit_threshold}, 止损金额={loss_threshold}")
            return jsonify({'success': True, 'rule': rule_dict, 'message': '全局止盈止损配置设置成功'})

    except Exception as e:
        logger.error(f"处理全局止盈止损规则失败: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'success': False, 'message': f'处理全局止盈止损规则失败: {str(e)}'})

# 路由: API - 测试Bark通知
@app.route('/api/test_bark_notification', methods=['POST'])
@login_required
def api_test_bark_notification():
    """测试Bark通知功能"""
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})

        notification_type = data.get('type', 'basic')

        # 加载配置
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 检查Bark配置
        bark_url = config.get('bark_url', '')
        bark_device_key = config.get('bark_device_key', '')
        bark_device_key_2 = config.get('bark_device_key_2', '')

        if not bark_url:
            return jsonify({'success': False, 'message': 'Bark服务地址未配置'})

        if not bark_device_key and not bark_device_key_2:
            return jsonify({'success': False, 'message': '至少需要配置一个Bark设备密钥'})

        # 导入通知模块
        from bark_notifier import send_bark_notification, notify_trade_execution, notify_trade_closed, notify_error

        # 根据类型发送不同的测试通知
        if notification_type == 'basic':
            title = "Bark通知测试"
            body = f"这是一条测试通知，发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n如果您收到此通知，说明Bark配置正确！"
            result = send_bark_notification(title, body, config)

        elif notification_type == 'trade':
            # 模拟交易通知
            test_trade = {
                'symbol': 'XAUUSD',
                'operation': 'buy',
                'volume': 0.1,
                'price': 1950.50,
                'sl': 1940.00,
                'tp': 1970.00,
                'ticket': 99999999
            }
            result = notify_trade_execution(test_trade)

        elif notification_type == 'error':
            error_message = "这是一条测试错误通知，用于验证错误通知功能是否正常工作。"
            result = notify_error(error_message)

        elif notification_type == 'signal':
            # 导入信号通知函数
            from bark_notifier import notify_signal_received, notify_signal_processing

            # 模拟信号接收通知
            test_signal_info = {
                'signal_id': 99999,
                'trading_pair': 'ETHUSD',
                'signal_type': 'buy',
                'interval': '1h',
                'mrc_event': 'entry'
            }
            result1 = notify_signal_received(test_signal_info)

            # 模拟信号处理通知
            test_signal_dict = {
                'id': 99999,
                'trading_pair': 'ETHUSD',
                'signal_type': 'buy'
            }
            result2 = notify_signal_processing(test_signal_dict, 'success', '测试信号处理成功')

            result = result1 and result2

        else:
            return jsonify({'success': False, 'message': f'不支持的通知类型: {notification_type}'})

        # 构建详细信息
        device_count = 0
        if bark_device_key:
            device_count += 1
        if bark_device_key_2:
            device_count += 1

        details = f"已配置 {device_count} 个设备"
        if device_count > 1:
            details += "，通知已发送到所有配置的设备"

        if result:
            message = f"{notification_type.title()}测试通知发送成功"
            return jsonify({
                'success': True,
                'message': message,
                'details': details
            })
        else:
            return jsonify({
                'success': False,
                'message': f"{notification_type.title()}测试通知发送失败，请检查配置和网络连接"
            })

    except Exception as e:
        logger.error(f"测试Bark通知失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'测试通知失败: {str(e)}'})

# 路由: API - 测试余额监测通知
@app.route('/api/test_balance_notification', methods=['POST'])
@login_required
def api_test_balance_notification():
    """测试余额监测通知功能"""
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})

        notification_type = data.get('type', 'balance_change')

        # 导入余额监测模块
        import balance_monitor

        # 获取当前账户信息
        account_info = balance_monitor.balance_monitor.get_account_info()
        if not account_info:
            return jsonify({'success': False, 'message': '无法获取账户信息，请检查MT5连接'})

        result = False

        if notification_type == 'balance_change':
            # 测试余额变动通知
            balance_monitor.balance_monitor.send_balance_change_notification(account_info, 25.50)
            result = True

        elif notification_type == 'profit':
            # 测试盈利通知
            balance_monitor.balance_monitor.send_profit_notification(account_info, 75.25)
            result = True

        elif notification_type == 'loss':
            # 测试亏损通知
            balance_monitor.balance_monitor.send_loss_notification(account_info, 60.80)
            result = True

        elif notification_type == 'periodic':
            # 测试定时余额推送
            balance_monitor.balance_monitor.send_periodic_balance_notification()
            result = True

        else:
            return jsonify({'success': False, 'message': f'不支持的通知类型: {notification_type}'})

        if result:
            return jsonify({
                'success': True,
                'message': f'{notification_type.title()}测试通知发送成功',
                'details': '通知已发送到两个Bark设备'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'{notification_type.title()}测试通知发送失败'
            })

    except Exception as e:
        logger.error(f"测试余额监测通知失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'测试通知失败: {str(e)}'})

# 路由: API - 获取余额监测状态
@app.route('/api/balance_monitor/status')
@login_required
def api_balance_monitor_status():
    """获取余额监测状态"""
    try:
        import balance_monitor
        status = balance_monitor.get_balance_monitoring_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"获取余额监测状态失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取监测状态失败: {str(e)}'})

# 路由: API - 启动/停止余额监测
@app.route('/api/balance_monitor/toggle', methods=['POST'])
@login_required
def api_balance_monitor_toggle():
    """启动或停止余额监测"""
    try:
        data = request.json
        action = data.get('action', 'start')

        import balance_monitor

        if action == 'start':
            balance_monitor.start_balance_monitoring()
            return jsonify({'success': True, 'message': '余额监测已启动'})
        elif action == 'stop':
            balance_monitor.stop_balance_monitoring()
            return jsonify({'success': True, 'message': '余额监测已停止'})
        else:
            return jsonify({'success': False, 'message': '无效的操作'})

    except Exception as e:
        logger.error(f"切换余额监测状态失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

# 路由: API - 获取余额历史
@app.route('/api/balance_history')
@login_required
def api_balance_history():
    """获取余额历史记录"""
    try:
        days = request.args.get('days', 7, type=int)

        import balance_monitor
        history = balance_monitor.get_balance_history(days)

        return jsonify({
            'success': True,
            'history': history
        })
    except Exception as e:
        logger.error(f"获取余额历史失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取历史失败: {str(e)}'})

# 路由: API - 获取浮动盈亏监控状态
@app.route('/api/floating_pl_monitor/status')
@login_required
def api_floating_pl_monitor_status():
    """获取浮动盈亏监控状态"""
    try:
        is_running = floating_pl_monitor.is_floating_pl_monitor_running()
        return jsonify({
            'success': True,
            'monitoring': is_running,
            'status': 'running' if is_running else 'stopped'
        })
    except Exception as e:
        logger.error(f"获取浮动盈亏监控状态失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'获取监控状态失败: {str(e)}'})

# 路由: API - 启动/停止浮动盈亏监控
@app.route('/api/floating_pl_monitor/control', methods=['POST'])
@login_required
def api_floating_pl_monitor_control():
    """启动或停止浮动盈亏监控"""
    try:
        data = request.json
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})
        
        action = data.get('action')
        
        if action == 'start':
            result = floating_pl_monitor.start_floating_pl_monitor()
            if result:
                return jsonify({'success': True, 'message': '浮动盈亏监控已启动'})
            else:
                return jsonify({'success': False, 'message': '启动浮动盈亏监控失败'})
        
        elif action == 'stop':
            result = floating_pl_monitor.stop_floating_pl_monitor()
            if result:
                return jsonify({'success': True, 'message': '浮动盈亏监控已停止'})
            else:
                return jsonify({'success': False, 'message': '停止浮动盈亏监控失败'})
        
        else:
            return jsonify({'success': False, 'message': '无效的操作类型'})
            
    except Exception as e:
        logger.error(f"控制浮动盈亏监控失败: {e}", exc_info=True)
        return jsonify({'success': False, 'message': f'控制监控失败: {str(e)}'})

if __name__ == '__main__':
    try:
        # 初始化登录安全数据库表
        logger.info("初始化登录安全数据库表...")
        init_login_security_tables()

        # 启动浮动盈亏监控
        logger.info("启动浮动盈亏监控...")
        if floating_pl_monitor.start_floating_pl_monitor():
            logger.info("浮动盈亏监控启动成功")
        else:
            logger.warning("浮动盈亏监控启动失败")

        # 启动余额监测服务
        logger.info("启动余额监测服务...")
        try:
            import balance_monitor
            balance_monitor.start_balance_monitoring()
            logger.info("余额监测服务启动成功")
        except Exception as e:
            logger.error(f"余额监测服务启动失败: {e}", exc_info=True)

        logger.info(f"Web界面启动，端口: {WEB_PORT}")
        app.run(host='0.0.0.0', port=WEB_PORT, debug=False)
    except Exception as e:
        logger.error(f"Web界面启动失败: {e}", exc_info=True)
    finally:
        # 确保在程序退出时停止监控
        try:
            floating_pl_monitor.stop_floating_pl_monitor()
            logger.info("浮动盈亏监控已停止")
        except Exception as e:
            logger.error(f"停止浮动盈亏监控失败: {e}", exc_info=True)

        # 停止余额监测服务
        try:
            import balance_monitor
            balance_monitor.stop_balance_monitoring()
            logger.info("余额监测服务已停止")
        except Exception as e:
            logger.error(f"停止余额监测服务失败: {e}", exc_info=True)
